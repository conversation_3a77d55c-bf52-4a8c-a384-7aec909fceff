'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Users, DollarSign, Calendar, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'
import Button from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { formatCurrency, formatDate, getStatusColor, getStatusText } from '@/lib/utils'
import { storage, initializeMockData } from '@/lib/storage'

// Mock data - replace with actual data from Supabase
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'أحمد محمد',
  avatar_url: null
}

const mockGroups = [
  {
    id: '1',
    name: 'صندوق الأصدقاء',
    member_count: 10,
    contribution_amount: 1000,
    status: 'active',
    next_disbursement: '2024-02-15',
    my_position: 3,
    current_cycle: 2
  },
  {
    id: '2',
    name: 'صندوق العائلة',
    member_count: 8,
    contribution_amount: 2000,
    status: 'draft',
    next_disbursement: null,
    my_position: null,
    current_cycle: 0
  }
]

const mockStats = {
  total_groups: 2,
  active_groups: 1,
  total_contributed: 3000,
  total_received: 0,
  pending_payments: 1000,
  next_disbursement: 16000
}

const mockRecentActivity = [
  {
    id: '1',
    type: 'payment',
    message: 'تم دفع مساهمة شهر فبراير لصندوق الأصدقاء',
    date: '2024-02-01',
    amount: 1000
  },
  {
    id: '2',
    type: 'disbursement',
    message: 'تم صرف مبلغ لمحمد علي من صندوق الأصدقاء',
    date: '2024-01-15',
    amount: 10000
  }
]

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null)
  const [groups, setGroups] = useState<any[]>([])
  const [stats, setStats] = useState<any>({})
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // تهيئة البيانات التجريبية
    initializeMockData()

    // تحميل البيانات من التخزين المحلي
    const currentUser = storage.getCurrentUser()
    const allGroups = storage.getGroups()
    const notifications = storage.getNotifications()

    // حساب الإحصائيات
    const activeGroups = allGroups.filter(g => g.status === 'active').length
    const totalContributed = allGroups.reduce((sum, group) => {
      return sum + (group.contribution_amount * group.current_cycle)
    }, 0)

    const calculatedStats = {
      total_groups: allGroups.length,
      active_groups: activeGroups,
      total_contributed: totalContributed,
      total_received: 0,
      pending_payments: 1000,
      next_disbursement: 16000
    }

    // تحويل الإشعارات إلى أنشطة حديثة
    const activities = notifications.slice(0, 5).map(notif => ({
      id: notif.id,
      type: notif.type === 'payment_due' ? 'payment' : 'disbursement',
      message: notif.message,
      date: notif.created_at,
      amount: notif.amount || 0
    }))

    setUser(currentUser)
    setGroups(allGroups)
    setStats(calculatedStats)
    setRecentActivity(activities)
    setLoading(false)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar user={user} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            مرحباً، {user.full_name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            إليك نظرة عامة على صناديقك التعاونية
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Users className="w-6 h-6 text-primary-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    إجمالي المجموعات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_groups}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    المجموعات النشطة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.active_groups}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    إجمالي المساهمات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(stats.total_contributed)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <AlertCircle className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    مدفوعات معلقة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(stats.pending_payments)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* My Groups */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>مجموعاتي</CardTitle>
                  <CardDescription>
                    الصناديق التعاونية التي تشارك فيها
                  </CardDescription>
                </div>
                <Link href="/groups/create">
                  <Button size="sm">
                    <Plus className="w-4 h-4 ml-2" />
                    إنشاء مجموعة
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {groups.map((group) => (
                    <div
                      key={group.id}
                      className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {group.name}
                          </h3>
                          <Badge
                            variant={
                              group.status === 'active' ? 'success' :
                              group.status === 'draft' ? 'warning' : 'default'
                            }
                          >
                            {getStatusText(group.status)}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                          <span>{group.member_count} أعضاء</span>
                          <span>{formatCurrency(group.contribution_amount)} شهرياً</span>
                          {group.my_position && (
                            <span>ترتيبي: {group.my_position}</span>
                          )}
                        </div>
                        {group.next_disbursement && (
                          <p className="text-sm text-primary-600 mt-1">
                            الصرف التالي: {formatDate(group.next_disbursement)}
                          </p>
                        )}
                      </div>
                      <Link href={`/groups/${group.id}`}>
                        <Button variant="outline" size="sm">
                          عرض التفاصيل
                        </Button>
                      </Link>
                    </div>
                  ))}
                  
                  {groups.length === 0 && (
                    <div className="text-center py-8">
                      <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        لم تنضم إلى أي مجموعة بعد
                      </p>
                      <Link href="/groups/create">
                        <Button>
                          <Plus className="w-4 h-4 ml-2" />
                          إنشاء مجموعة جديدة
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>النشاط الأخير</CardTitle>
                <CardDescription>
                  آخر المعاملات والأنشطة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 space-x-reverse">
                      <div className={`p-2 rounded-full ${
                        activity.type === 'payment' ? 'bg-green-100' : 'bg-blue-100'
                      }`}>
                        {activity.type === 'payment' ? (
                          <DollarSign className="w-4 h-4 text-green-600" />
                        ) : (
                          <TrendingUp className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 dark:text-white">
                          {activity.message}
                        </p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            {formatDate(activity.date)}
                          </p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(activity.amount)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {recentActivity.length === 0 && (
                    <div className="text-center py-4">
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        لا توجد أنشطة حديثة
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
