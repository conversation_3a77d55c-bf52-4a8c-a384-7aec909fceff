const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل تطبيق صرفة...');

const nextPath = path.join(__dirname, 'node_modules', '.bin', 'next');
const child = spawn('node', [nextPath, 'dev'], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('❌ خطأ في تشغيل التطبيق:', error);
});

child.on('close', (code) => {
  console.log(`✅ تم إغلاق التطبيق برمز: ${code}`);
});

console.log('📱 التطبيق يعمل على: http://localhost:3000');
