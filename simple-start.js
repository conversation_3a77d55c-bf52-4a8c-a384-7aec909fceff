// ملف تشغيل مبسط لتطبيق صرفة
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// HTML بسيط لاختبار التطبيق
const testHTML = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صرفة - اختبار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #333;
            font-size: 3em;
            margin-bottom: 20px;
        }
        .status {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.2em;
        }
        .info {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        .currency {
            background: #2196F3;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.3em;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 صرفة</h1>
        
        <div class="status">
            ✅ التطبيق يعمل بنجاح!
        </div>
        
        <div class="currency">
            💰 العملة: الجنيه المصري (EGP)
        </div>
        
        <div class="info">
            <h3>📋 معلومات التطبيق:</h3>
            <p><strong>الاسم:</strong> صرفة - إدارة الصندوق التعاوني</p>
            <p><strong>الإصدار:</strong> 1.0.0</p>
            <p><strong>المنفذ:</strong> ${PORT}</p>
            <p><strong>الحالة:</strong> جاهز للاستخدام</p>
        </div>
        
        <div>
            <h3>🚀 الميزات المتاحة:</h3>
            <a href="#" class="btn">إنشاء مجموعة جديدة</a>
            <a href="#" class="btn">عرض المجموعات</a>
            <a href="#" class="btn">الإشعارات</a>
        </div>
        
        <div class="info">
            <h3>💡 ملاحظة:</h3>
            <p>هذا اختبار مبدئي للتطبيق. جميع المبالغ تظهر بالجنيه المصري.</p>
            <p>لتشغيل التطبيق الكامل، استخدم: <code>npm run dev</code></p>
        </div>
    </div>
</body>
</html>
`;

const server = http.createServer((req, res) => {
    res.writeHead(200, {
        'Content-Type': 'text/html; charset=utf-8',
        'Access-Control-Allow-Origin': '*'
    });
    res.end(testHTML);
});

server.listen(PORT, () => {
    console.log('🚀 تطبيق صرفة يعمل على:');
    console.log(`📱 http://localhost:${PORT}`);
    console.log('💰 العملة: الجنيه المصري');
    console.log('✅ جاهز للاستخدام!');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ المنفذ ${PORT} مستخدم بالفعل`);
        console.log('جرب منفذ آخر أو أغلق التطبيق الآخر');
    } else {
        console.error('❌ خطأ في الخادم:', err);
    }
});
