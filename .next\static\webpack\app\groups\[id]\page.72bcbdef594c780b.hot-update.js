"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./src/app/groups/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/groups/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroup = {\n    id: \"1\",\n    name: \"صندوق الأصدقاء\",\n    description: \"صندوق تعاوني للأصدقاء المقربين\",\n    admin_id: \"1\",\n    member_count: 10,\n    contribution_amount: 1000,\n    cycle_duration_months: 10,\n    status: \"active\",\n    start_date: \"2024-01-01\",\n    end_date: \"2024-10-31\",\n    draw_completed: true,\n    current_cycle: 2,\n    created_at: \"2023-12-15\"\n};\nconst mockMembers = [\n    {\n        id: \"1\",\n        user_id: \"1\",\n        full_name: \"أحمد محمد\",\n        position_in_draw: 3,\n        is_admin: true,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        user_id: \"2\",\n        full_name: \"محمد علي\",\n        position_in_draw: 1,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        user_id: \"3\",\n        full_name: \"سارة أحمد\",\n        position_in_draw: 2,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"4\",\n        user_id: \"4\",\n        full_name: \"فاطمة محمد\",\n        position_in_draw: 4,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"5\",\n        user_id: \"5\",\n        full_name: \"عبدالله سالم\",\n        position_in_draw: 5,\n        is_admin: false,\n        status: \"active\"\n    }\n];\nconst mockPayments = [\n    {\n        id: \"1\",\n        member_name: \"أحمد محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-01\"\n    },\n    {\n        id: \"2\",\n        member_name: \"محمد علي\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-02\"\n    },\n    {\n        id: \"3\",\n        member_name: \"سارة أحمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"pending\",\n        due_date: \"2024-02-15\"\n    },\n    {\n        id: \"4\",\n        member_name: \"فاطمة محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"overdue\",\n        due_date: \"2024-02-10\"\n    }\n];\nfunction GroupDetailsPage(param) {\n    let { params } = param;\n    _s();\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showLotteryModal, setShowLotteryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAdmin = (group === null || group === void 0 ? void 0 : group.admin_id) === (user === null || user === void 0 ? void 0 : user.id);\n    const totalCollected = payments.filter((p)=>p.status === \"paid\").length * group.contribution_amount;\n    const pendingPayments = payments.filter((p)=>p.status === \"pending\").length;\n    const overduePayments = payments.filter((p)=>p.status === \"overdue\").length;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // TODO: Fetch actual data from Supabase\n        setLoading(false);\n    }, [\n        params.id\n    ]);\n    const handleDrawLottery = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Implement lottery draw logic\n            console.log(\"Drawing lottery for group:\", params.id);\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Update group state\n        } catch (error) {\n            console.error(\"Lottery draw error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إعدادات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"الأعضاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: group.member_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المبلغ المجمع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(totalCollected)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: overduePayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 space-x-reverse\",\n                                children: [\n                                    {\n                                        id: \"overview\",\n                                        name: \"نظرة عامة\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                    },\n                                    {\n                                        id: \"members\",\n                                        name: \"الأعضاء\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                    },\n                                    {\n                                        id: \"payments\",\n                                        name: \"المدفوعات\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات المجموعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"المساهمة الشهرية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدة الدورة:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.cycle_duration_months,\n                                                            \" شهر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ البداية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.start_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ النهاية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.end_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"الدورة الحالية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.current_cycle,\n                                                            \" من \",\n                                                            group.cycle_duration_months\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"القرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"ترتيب الصرف للأعضاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: group.draw_completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: members.sort((a, b)=>(a.position_in_draw || 0) - (b.position_in_draw || 0)).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-50 border border-primary-200\" : \"bg-gray-50 dark:bg-gray-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-600 text-white\" : \"bg-gray-200 text-gray-700\"),\n                                                                    children: member.position_in_draw\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-3 font-medium\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"info\",\n                                                                    className: \"mr-2\",\n                                                                    children: \"مسؤول\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        member.position_in_draw < group.current_cycle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, member.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"لم يتم إجراء القرعة بعد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onClick: handleDrawLottery,\n                                                    loading: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"إجراء القرعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"members\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"أعضاء المجموعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"قائمة بجميع أعضاء الصندوق التعاوني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 font-medium\",\n                                                                children: member.full_name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: member.position_in_draw ? \"الترتيب: \".concat(member.position_in_draw) : \"لم يتم تحديد الترتيب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: member.status === \"active\" ? \"success\" : \"default\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(member.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"payments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"مدفوعات الدورة الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: [\n                                            \"حالة المدفوعات للدورة رقم \",\n                                            group.current_cycle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: payments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: payment.member_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: payment.status === \"paid\" && payment.paid_date ? \"تم الدفع في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.paid_date)) : payment.status === \"pending\" ? \"مستحق في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date)) : \"متأخر منذ \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(payment.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: payment.status === \"paid\" ? \"success\" : payment.status === \"pending\" ? \"warning\" : \"danger\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(payment.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin && payment.status !== \"paid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"تأكيد الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, payment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetailsPage, \"1HTPcEVpDd+PhyViXfW7dgFeX5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GroupDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/[id]/page.tsx\n"));

/***/ })

});