<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صرفة - إدارة الصندوق التعاوني</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.3em;
            color: #666;
        }
        
        .currency-badge {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            display: inline-block;
            margin: 20px 0;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2em;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.1em;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .summary h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .summary-item:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.2em;
            color: #667eea;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>صرفة</h1>
            <p>تطبيق إدارة الصناديق التعاونية الدورية</p>
            <div class="currency-badge">
                💰 العملة: الجنيه المصري (EGP)
            </div>
        </div>
        
        <!-- Features -->
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>إدارة الأعضاء</h3>
                <p>إضافة وإدارة أعضاء المجموعة بسهولة مع تتبع مساهماتهم</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>تتبع المساهمات</h3>
                <p>متابعة المدفوعات والمبالغ المستحقة بالجنيه المصري</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎲</div>
                <h3>نظام القرعة</h3>
                <p>قرعة عادلة لتحديد ترتيب الصرف بطريقة شفافة</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔔</div>
                <h3>الإشعارات</h3>
                <p>تذكيرات تلقائية للمواعيد والمدفوعات المستحقة</p>
            </div>
        </div>
        
        <!-- Demo Form -->
        <div class="demo-section">
            <h2>🚀 تجربة إنشاء مجموعة جديدة</h2>
            
            <div class="form-group">
                <label for="groupName">اسم المجموعة:</label>
                <input type="text" id="groupName" placeholder="مثال: صندوق الأصدقاء" value="صندوق الأصدقاء">
            </div>
            
            <div class="form-group">
                <label for="memberCount">عدد الأعضاء:</label>
                <input type="number" id="memberCount" placeholder="10" value="10" min="2" max="50">
            </div>
            
            <div class="form-group">
                <label for="contribution">مبلغ المساهمة الشهرية (جنيه):</label>
                <input type="number" id="contribution" placeholder="500" value="500" min="1">
            </div>
            
            <div class="form-group">
                <label for="duration">مدة الدورة (بالأشهر):</label>
                <input type="number" id="duration" placeholder="10" value="10" min="1">
            </div>
            
            <div class="summary">
                <h3>📊 ملخص المجموعة:</h3>
                <div class="summary-item">
                    <span>عدد الأعضاء:</span>
                    <span id="summaryMembers">10 عضو</span>
                </div>
                <div class="summary-item">
                    <span>المساهمة الشهرية:</span>
                    <span id="summaryContribution">500 جنيه</span>
                </div>
                <div class="summary-item">
                    <span>مدة الدورة:</span>
                    <span id="summaryDuration">10 شهر</span>
                </div>
                <div class="summary-item">
                    <span>إجمالي المبلغ الشهري:</span>
                    <span id="summaryTotal">5,000 جنيه</span>
                </div>
            </div>
            
            <button class="btn" onclick="createGroup()">✨ إنشاء المجموعة</button>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>© 2024 صرفة - جميع الحقوق محفوظة</p>
            <p>تطبيق إدارة الصناديق التعاونية بالجنيه المصري</p>
        </div>
    </div>
    
    <script>
        // تحديث الملخص عند تغيير القيم
        function updateSummary() {
            const members = document.getElementById('memberCount').value || 0;
            const contribution = document.getElementById('contribution').value || 0;
            const duration = document.getElementById('duration').value || 0;
            const total = members * contribution;
            
            document.getElementById('summaryMembers').textContent = members + ' عضو';
            document.getElementById('summaryContribution').textContent = contribution + ' جنيه';
            document.getElementById('summaryDuration').textContent = duration + ' شهر';
            document.getElementById('summaryTotal').textContent = total.toLocaleString() + ' جنيه';
        }
        
        // ربط الأحداث
        document.getElementById('memberCount').addEventListener('input', updateSummary);
        document.getElementById('contribution').addEventListener('input', updateSummary);
        document.getElementById('duration').addEventListener('input', updateSummary);
        
        // دالة إنشاء المجموعة
        function createGroup() {
            const groupName = document.getElementById('groupName').value;
            const members = document.getElementById('memberCount').value;
            const contribution = document.getElementById('contribution').value;
            const duration = document.getElementById('duration').value;
            
            if (!groupName || !members || !contribution || !duration) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            alert(`✅ تم إنشاء مجموعة "${groupName}" بنجاح!\n\n` +
                  `📊 التفاصيل:\n` +
                  `• الأعضاء: ${members}\n` +
                  `• المساهمة: ${contribution} جنيه\n` +
                  `• المدة: ${duration} شهر\n` +
                  `• الإجمالي الشهري: ${(members * contribution).toLocaleString()} جنيه`);
        }
        
        // تحديث أولي
        updateSummary();
    </script>
</body>
</html>
