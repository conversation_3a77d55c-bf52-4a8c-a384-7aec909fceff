"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./src/app/groups/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/groups/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroup = {\n    id: \"1\",\n    name: \"صندوق الأصدقاء\",\n    description: \"صندوق تعاوني للأصدقاء المقربين\",\n    admin_id: \"1\",\n    member_count: 10,\n    contribution_amount: 1000,\n    cycle_duration_months: 10,\n    status: \"active\",\n    start_date: \"2024-01-01\",\n    end_date: \"2024-10-31\",\n    draw_completed: true,\n    current_cycle: 2,\n    created_at: \"2023-12-15\"\n};\nconst mockMembers = [\n    {\n        id: \"1\",\n        user_id: \"1\",\n        full_name: \"أحمد محمد\",\n        position_in_draw: 3,\n        is_admin: true,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        user_id: \"2\",\n        full_name: \"محمد علي\",\n        position_in_draw: 1,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        user_id: \"3\",\n        full_name: \"سارة أحمد\",\n        position_in_draw: 2,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"4\",\n        user_id: \"4\",\n        full_name: \"فاطمة محمد\",\n        position_in_draw: 4,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"5\",\n        user_id: \"5\",\n        full_name: \"عبدالله سالم\",\n        position_in_draw: 5,\n        is_admin: false,\n        status: \"active\"\n    }\n];\nconst mockPayments = [\n    {\n        id: \"1\",\n        member_name: \"أحمد محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-01\"\n    },\n    {\n        id: \"2\",\n        member_name: \"محمد علي\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-02\"\n    },\n    {\n        id: \"3\",\n        member_name: \"سارة أحمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"pending\",\n        due_date: \"2024-02-15\"\n    },\n    {\n        id: \"4\",\n        member_name: \"فاطمة محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"overdue\",\n        due_date: \"2024-02-10\"\n    }\n];\nfunction GroupDetailsPage(param) {\n    let { params } = param;\n    _s();\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showLotteryModal, setShowLotteryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAdmin = (group === null || group === void 0 ? void 0 : group.admin_id) === (user === null || user === void 0 ? void 0 : user.id);\n    const totalCollected = payments.filter((p)=>p.status === \"paid\").length * group.contribution_amount;\n    const pendingPayments = payments.filter((p)=>p.status === \"pending\").length;\n    const overduePayments = payments.filter((p)=>p.status === \"overdue\").length;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getCurrentUser();\n        const groupData = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getGroupById(params.id);\n        const groupMembers = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembersByGroupId(params.id);\n        const groupPayments = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getPaymentsByGroupId(params.id);\n        setUser(currentUser);\n        setGroup(groupData);\n        setMembers(groupMembers);\n        setPayments(groupPayments);\n        setLoading(false);\n    }, [\n        params.id\n    ]);\n    const handleDrawLottery = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Implement lottery draw logic\n            console.log(\"Drawing lottery for group:\", params.id);\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Update group state\n        } catch (error) {\n            console.error(\"Lottery draw error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إعدادات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"الأعضاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: group.member_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المبلغ المجمع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(totalCollected)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: overduePayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 space-x-reverse\",\n                                children: [\n                                    {\n                                        id: \"overview\",\n                                        name: \"نظرة عامة\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                    },\n                                    {\n                                        id: \"members\",\n                                        name: \"الأعضاء\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    },\n                                    {\n                                        id: \"payments\",\n                                        name: \"المدفوعات\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات المجموعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"المساهمة الشهرية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدة الدورة:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.cycle_duration_months,\n                                                            \" شهر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ البداية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.start_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ النهاية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.end_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"الدورة الحالية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.current_cycle,\n                                                            \" من \",\n                                                            group.cycle_duration_months\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"القرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"ترتيب الصرف للأعضاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: group.draw_completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: members.sort((a, b)=>(a.position_in_draw || 0) - (b.position_in_draw || 0)).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-50 border border-primary-200\" : \"bg-gray-50 dark:bg-gray-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-600 text-white\" : \"bg-gray-200 text-gray-700\"),\n                                                                    children: member.position_in_draw\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-3 font-medium\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"info\",\n                                                                    className: \"mr-2\",\n                                                                    children: \"مسؤول\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        member.position_in_draw < group.current_cycle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, member.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"لم يتم إجراء القرعة بعد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onClick: handleDrawLottery,\n                                                    loading: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"إجراء القرعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"members\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"أعضاء المجموعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"قائمة بجميع أعضاء الصندوق التعاوني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 font-medium\",\n                                                                children: member.full_name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: member.position_in_draw ? \"الترتيب: \".concat(member.position_in_draw) : \"لم يتم تحديد الترتيب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: member.status === \"active\" ? \"success\" : \"default\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(member.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"payments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"مدفوعات الدورة الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: [\n                                            \"حالة المدفوعات للدورة رقم \",\n                                            group.current_cycle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: payments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: payment.member_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: payment.status === \"paid\" && payment.paid_date ? \"تم الدفع في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.paid_date)) : payment.status === \"pending\" ? \"مستحق في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date)) : \"متأخر منذ \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(payment.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: payment.status === \"paid\" ? \"success\" : payment.status === \"pending\" ? \"warning\" : \"danger\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(payment.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin && payment.status !== \"paid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"تأكيد الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, payment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetailsPage, \"1HTPcEVpDd+PhyViXfW7dgFeX5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GroupDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeMockData: function() { return /* binding */ initializeMockData; },\n/* harmony export */   storage: function() { return /* binding */ storage; }\n/* harmony export */ });\n// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    GROUPS: \"sarfa_groups\",\n    MEMBERS: \"sarfa_members\",\n    PAYMENTS: \"sarfa_payments\",\n    NOTIFICATIONS: \"sarfa_notifications\",\n    CURRENT_USER: \"sarfa_current_user\"\n};\n// دوال مساعدة للتخزين المحلي\nconst storage = {\n    // المجموعات\n    getGroups: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.GROUPS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveGroups: (groups)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups));\n    },\n    addGroup: (group)=>{\n        const newGroup = {\n            ...group,\n            id: generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const groups = storage.getGroups();\n        groups.push(newGroup);\n        storage.saveGroups(groups);\n        return newGroup;\n    },\n    updateGroup: (id, updates)=>{\n        const groups = storage.getGroups();\n        const index = groups.findIndex((g)=>g.id === id);\n        if (index === -1) return null;\n        groups[index] = {\n            ...groups[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        storage.saveGroups(groups);\n        return groups[index];\n    },\n    getGroupById: (id)=>{\n        const groups = storage.getGroups();\n        return groups.find((g)=>g.id === id) || null;\n    },\n    // الأعضاء\n    getMembers: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveMembers: (members)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));\n    },\n    getMembersByGroupId: (groupId)=>{\n        return storage.getMembers().filter((m)=>m.group_id === groupId);\n    },\n    addMember: (member)=>{\n        const newMember = {\n            ...member,\n            id: generateId(),\n            joined_at: new Date().toISOString()\n        };\n        const members = storage.getMembers();\n        members.push(newMember);\n        storage.saveMembers(members);\n        return newMember;\n    },\n    // المدفوعات\n    getPayments: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS);\n        return data ? JSON.parse(data) : [];\n    },\n    savePayments: (payments)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments));\n    },\n    getPaymentsByGroupId: (groupId)=>{\n        return storage.getPayments().filter((p)=>p.group_id === groupId);\n    },\n    // الإشعارات\n    getNotifications: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveNotifications: (notifications)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    },\n    addNotification: (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: generateId(),\n            created_at: new Date().toISOString()\n        };\n        const notifications = storage.getNotifications();\n        notifications.unshift(newNotification) // إضافة في المقدمة\n        ;\n        storage.saveNotifications(notifications);\n        return newNotification;\n    },\n    // المستخدم الحالي\n    getCurrentUser: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n        return data ? JSON.parse(data) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));\n    },\n    // مسح جميع البيانات\n    clearAll: ()=>{\n        if (false) {}\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n    }\n};\n// دالة توليد معرف فريد\nfunction generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n// تهيئة البيانات التجريبية\nconst initializeMockData = ()=>{\n    if (false) {}\n    // التحقق من وجود بيانات مسبقة\n    const existingGroups = storage.getGroups();\n    if (existingGroups.length > 0) return;\n    // إنشاء مستخدم تجريبي\n    const mockUser = {\n        id: \"user_1\",\n        email: \"<EMAIL>\",\n        full_name: \"أحمد محمد\",\n        avatar_url: null\n    };\n    storage.setCurrentUser(mockUser);\n    // إنشاء مجموعات تجريبية\n    const mockGroups = [\n        {\n            id: \"group_1\",\n            name: \"صندوق الأصدقاء\",\n            description: \"صندوق تعاوني للأصدقاء المقربين\",\n            admin_id: \"user_1\",\n            member_count: 10,\n            contribution_amount: 1000,\n            cycle_duration_months: 10,\n            status: \"active\",\n            start_date: \"2024-01-01\",\n            end_date: \"2024-10-31\",\n            draw_completed: true,\n            current_cycle: 2,\n            created_at: \"2023-12-15T00:00:00Z\",\n            updated_at: \"2023-12-15T00:00:00Z\"\n        }\n    ];\n    storage.saveGroups(mockGroups);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3RvcmFnZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHVEQUF1RDtBQTJEdkQsd0JBQXdCO0FBQ3hCLE1BQU1BLGVBQWU7SUFDbkJDLFFBQVE7SUFDUkMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLGVBQWU7SUFDZkMsY0FBYztBQUNoQjtBQUVBLDZCQUE2QjtBQUN0QixNQUFNQyxVQUFVO0lBQ3JCLFlBQVk7SUFDWkMsV0FBVztRQUNULElBQUksS0FBa0IsRUFBYSxFQUFTO1FBQzVDLE1BQU1DLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUMsTUFBTTtRQUNyRCxPQUFPTyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBSyxZQUFZLENBQUNDO1FBQ1gsSUFBSSxLQUFrQixFQUFhO1FBQ25DTCxhQUFhTSxPQUFPLENBQUNmLGFBQWFDLE1BQU0sRUFBRVUsS0FBS0ssU0FBUyxDQUFDRjtJQUMzRDtJQUVBRyxVQUFVLENBQUNDO1FBQ1QsTUFBTUMsV0FBa0I7WUFDdEIsR0FBR0QsS0FBSztZQUNSRSxJQUFJQztZQUNKQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7WUFDbENDLFlBQVksSUFBSUYsT0FBT0MsV0FBVztRQUNwQztRQUVBLE1BQU1WLFNBQVNSLFFBQVFDLFNBQVM7UUFDaENPLE9BQU9ZLElBQUksQ0FBQ1A7UUFDWmIsUUFBUU8sVUFBVSxDQUFDQztRQUVuQixPQUFPSztJQUNUO0lBRUFRLGFBQWEsQ0FBQ1AsSUFBWVE7UUFDeEIsTUFBTWQsU0FBU1IsUUFBUUMsU0FBUztRQUNoQyxNQUFNc0IsUUFBUWYsT0FBT2dCLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVgsRUFBRSxLQUFLQTtRQUU3QyxJQUFJUyxVQUFVLENBQUMsR0FBRyxPQUFPO1FBRXpCZixNQUFNLENBQUNlLE1BQU0sR0FBRztZQUNkLEdBQUdmLE1BQU0sQ0FBQ2UsTUFBTTtZQUNoQixHQUFHRCxPQUFPO1lBQ1ZILFlBQVksSUFBSUYsT0FBT0MsV0FBVztRQUNwQztRQUVBbEIsUUFBUU8sVUFBVSxDQUFDQztRQUNuQixPQUFPQSxNQUFNLENBQUNlLE1BQU07SUFDdEI7SUFFQUcsY0FBYyxDQUFDWjtRQUNiLE1BQU1OLFNBQVNSLFFBQVFDLFNBQVM7UUFDaEMsT0FBT08sT0FBT21CLElBQUksQ0FBQ0YsQ0FBQUEsSUFBS0EsRUFBRVgsRUFBRSxLQUFLQSxPQUFPO0lBQzFDO0lBRUEsVUFBVTtJQUNWYyxZQUFZO1FBQ1YsSUFBSSxLQUFrQixFQUFhLEVBQVM7UUFDNUMsTUFBTTFCLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUUsT0FBTztRQUN0RCxPQUFPTSxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBMkIsYUFBYSxDQUFDQztRQUNaLElBQUksS0FBa0IsRUFBYTtRQUNuQzNCLGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUUsT0FBTyxFQUFFUyxLQUFLSyxTQUFTLENBQUNvQjtJQUM1RDtJQUVBQyxxQkFBcUIsQ0FBQ0M7UUFDcEIsT0FBT2hDLFFBQVE0QixVQUFVLEdBQUdLLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxLQUFLSDtJQUN6RDtJQUVBSSxXQUFXLENBQUNDO1FBQ1YsTUFBTUMsWUFBb0I7WUFDeEIsR0FBR0QsTUFBTTtZQUNUdkIsSUFBSUM7WUFDSndCLFdBQVcsSUFBSXRCLE9BQU9DLFdBQVc7UUFDbkM7UUFFQSxNQUFNWSxVQUFVOUIsUUFBUTRCLFVBQVU7UUFDbENFLFFBQVFWLElBQUksQ0FBQ2tCO1FBQ2J0QyxRQUFRNkIsV0FBVyxDQUFDQztRQUVwQixPQUFPUTtJQUNUO0lBRUEsWUFBWTtJQUNaRSxhQUFhO1FBQ1gsSUFBSSxLQUFrQixFQUFhLEVBQVM7UUFDNUMsTUFBTXRDLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUcsUUFBUTtRQUN2RCxPQUFPSyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBdUMsY0FBYyxDQUFDQztRQUNiLElBQUksS0FBa0IsRUFBYTtRQUNuQ3ZDLGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUcsUUFBUSxFQUFFUSxLQUFLSyxTQUFTLENBQUNnQztJQUM3RDtJQUVBQyxzQkFBc0IsQ0FBQ1g7UUFDckIsT0FBT2hDLFFBQVF3QyxXQUFXLEdBQUdQLE1BQU0sQ0FBQ1csQ0FBQUEsSUFBS0EsRUFBRVQsUUFBUSxLQUFLSDtJQUMxRDtJQUVBLFlBQVk7SUFDWmEsa0JBQWtCO1FBQ2hCLElBQUksS0FBa0IsRUFBYSxFQUFTO1FBQzVDLE1BQU0zQyxPQUFPQyxhQUFhQyxPQUFPLENBQUNWLGFBQWFJLGFBQWE7UUFDNUQsT0FBT0ksT0FBT0csS0FBS0MsS0FBSyxDQUFDSixRQUFRLEVBQUU7SUFDckM7SUFFQTRDLG1CQUFtQixDQUFDQztRQUNsQixJQUFJLEtBQWtCLEVBQWE7UUFDbkM1QyxhQUFhTSxPQUFPLENBQUNmLGFBQWFJLGFBQWEsRUFBRU8sS0FBS0ssU0FBUyxDQUFDcUM7SUFDbEU7SUFFQUMsaUJBQWlCLENBQUNDO1FBQ2hCLE1BQU1DLGtCQUFnQztZQUNwQyxHQUFHRCxZQUFZO1lBQ2ZuQyxJQUFJQztZQUNKQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7UUFDcEM7UUFFQSxNQUFNNkIsZ0JBQWdCL0MsUUFBUTZDLGdCQUFnQjtRQUM5Q0UsY0FBY0ksT0FBTyxDQUFDRCxpQkFBaUIsbUJBQW1COztRQUMxRGxELFFBQVE4QyxpQkFBaUIsQ0FBQ0M7UUFFMUIsT0FBT0c7SUFDVDtJQUVBLGtCQUFrQjtJQUNsQkUsZ0JBQWdCO1FBQ2QsSUFBSSxLQUFrQixFQUFhLEVBQU87UUFDMUMsTUFBTWxELE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUssWUFBWTtRQUMzRCxPQUFPRyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVE7SUFDbkM7SUFFQW1ELGdCQUFnQixDQUFDQztRQUNmLElBQUksS0FBa0IsRUFBYTtRQUNuQ25ELGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUssWUFBWSxFQUFFTSxLQUFLSyxTQUFTLENBQUM0QztJQUNqRTtJQUVBLG9CQUFvQjtJQUNwQkMsVUFBVTtRQUNSLElBQUksS0FBa0IsRUFBYTtRQUNuQ0MsT0FBT0MsTUFBTSxDQUFDL0QsY0FBY2dFLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDbEN4RCxhQUFheUQsVUFBVSxDQUFDRDtRQUMxQjtJQUNGO0FBQ0YsRUFBQztBQUVELHVCQUF1QjtBQUN2QixTQUFTNUM7SUFDUCxPQUFPRSxLQUFLNEMsR0FBRyxHQUFHQyxRQUFRLENBQUMsTUFBTUMsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsTUFBTSxDQUFDO0FBQ3JFO0FBRUEsMkJBQTJCO0FBQ3BCLE1BQU1DLHFCQUFxQjtJQUNoQyxJQUFJLEtBQWtCLEVBQWE7SUFFbkMsOEJBQThCO0lBQzlCLE1BQU1DLGlCQUFpQm5FLFFBQVFDLFNBQVM7SUFDeEMsSUFBSWtFLGVBQWVDLE1BQU0sR0FBRyxHQUFHO0lBRS9CLHNCQUFzQjtJQUN0QixNQUFNQyxXQUFXO1FBQ2Z2RCxJQUFJO1FBQ0p3RCxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsWUFBWTtJQUNkO0lBQ0F4RSxRQUFRcUQsY0FBYyxDQUFDZ0I7SUFFdkIsd0JBQXdCO0lBQ3hCLE1BQU1JLGFBQXNCO1FBQzFCO1lBQ0UzRCxJQUFJO1lBQ0o0RCxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLHFCQUFxQjtZQUNyQkMsdUJBQXVCO1lBQ3ZCQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWkMsVUFBVTtZQUNWQyxnQkFBZ0I7WUFDaEJDLGVBQWU7WUFDZnBFLFlBQVk7WUFDWkcsWUFBWTtRQUNkO0tBQ0Q7SUFFRG5CLFFBQVFPLFVBQVUsQ0FBQ2tFO0FBQ3JCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9zdG9yYWdlLnRzP2FmZTgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g2YbYuNin2YUg2KrYrtiy2YrZhiDZhdit2YTZiiDZhNmE2KjZitin2YbYp9iqICjZhdik2YLYqiDYrdiq2Ykg2YrYqtmFINix2KjYtyBTdXBhYmFzZSlcblxuZXhwb3J0IGludGVyZmFjZSBHcm91cCB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsXG4gIGFkbWluX2lkOiBzdHJpbmdcbiAgbWVtYmVyX2NvdW50OiBudW1iZXJcbiAgY29udHJpYnV0aW9uX2Ftb3VudDogbnVtYmVyXG4gIGN5Y2xlX2R1cmF0aW9uX21vbnRoczogbnVtYmVyXG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdhY3RpdmUnIHwgJ2NvbXBsZXRlZCcgfCAnY2FuY2VsbGVkJ1xuICBzdGFydF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIGVuZF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIGRyYXdfY29tcGxldGVkOiBib29sZWFuXG4gIGN1cnJlbnRfY3ljbGU6IG51bWJlclxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVtYmVyIHtcbiAgaWQ6IHN0cmluZ1xuICBncm91cF9pZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICBwb3NpdGlvbl9pbl9kcmF3OiBudW1iZXIgfCBudWxsXG4gIGlzX2FkbWluOiBib29sZWFuXG4gIGpvaW5lZF9hdDogc3RyaW5nXG4gIHN0YXR1czogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnIHwgJ3JlbW92ZWQnXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGF5bWVudCB7XG4gIGlkOiBzdHJpbmdcbiAgZ3JvdXBfaWQ6IHN0cmluZ1xuICBtZW1iZXJfaWQ6IHN0cmluZ1xuICBtZW1iZXJfbmFtZTogc3RyaW5nXG4gIGN5Y2xlX251bWJlcjogbnVtYmVyXG4gIGFtb3VudDogbnVtYmVyXG4gIGR1ZV9kYXRlOiBzdHJpbmdcbiAgcGFpZF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ3BhaWQnIHwgJ292ZXJkdWUnXG4gIHBheW1lbnRfbWV0aG9kOiBzdHJpbmcgfCBudWxsXG4gIG5vdGVzOiBzdHJpbmcgfCBudWxsXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBOb3RpZmljYXRpb24ge1xuICBpZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBncm91cF9pZDogc3RyaW5nIHwgbnVsbFxuICBncm91cF9uYW1lPzogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIHR5cGU6ICdwYXltZW50X2R1ZScgfCAncGF5bWVudF9yZWNlaXZlZCcgfCAnZGlzYnVyc2VtZW50JyB8ICdnZW5lcmFsJ1xuICBhbW91bnQ/OiBudW1iZXJcbiAgcmVhZDogYm9vbGVhblxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuLy8g2YXZgdin2KrZititINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuY29uc3QgU1RPUkFHRV9LRVlTID0ge1xuICBHUk9VUFM6ICdzYXJmYV9ncm91cHMnLFxuICBNRU1CRVJTOiAnc2FyZmFfbWVtYmVycycsXG4gIFBBWU1FTlRTOiAnc2FyZmFfcGF5bWVudHMnLFxuICBOT1RJRklDQVRJT05TOiAnc2FyZmFfbm90aWZpY2F0aW9ucycsXG4gIENVUlJFTlRfVVNFUjogJ3NhcmZhX2N1cnJlbnRfdXNlcidcbn1cblxuLy8g2K/ZiNin2YQg2YXYs9in2LnYr9ipINmE2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuZXhwb3J0IGNvbnN0IHN0b3JhZ2UgPSB7XG4gIC8vINin2YTZhdis2YXZiNi52KfYqlxuICBnZXRHcm91cHM6ICgpOiBHcm91cFtdID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBbXVxuICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWVMuR1JPVVBTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZUdyb3VwczogKGdyb3VwczogR3JvdXBbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkdST1VQUywgSlNPTi5zdHJpbmdpZnkoZ3JvdXBzKSlcbiAgfSxcblxuICBhZGRHcm91cDogKGdyb3VwOiBPbWl0PEdyb3VwLCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IEdyb3VwID0+IHtcbiAgICBjb25zdCBuZXdHcm91cDogR3JvdXAgPSB7XG4gICAgICAuLi5ncm91cCxcbiAgICAgIGlkOiBnZW5lcmF0ZUlkKCksXG4gICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gICAgXG4gICAgY29uc3QgZ3JvdXBzID0gc3RvcmFnZS5nZXRHcm91cHMoKVxuICAgIGdyb3Vwcy5wdXNoKG5ld0dyb3VwKVxuICAgIHN0b3JhZ2Uuc2F2ZUdyb3Vwcyhncm91cHMpXG4gICAgXG4gICAgcmV0dXJuIG5ld0dyb3VwXG4gIH0sXG5cbiAgdXBkYXRlR3JvdXA6IChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEdyb3VwPik6IEdyb3VwIHwgbnVsbCA9PiB7XG4gICAgY29uc3QgZ3JvdXBzID0gc3RvcmFnZS5nZXRHcm91cHMoKVxuICAgIGNvbnN0IGluZGV4ID0gZ3JvdXBzLmZpbmRJbmRleChnID0+IGcuaWQgPT09IGlkKVxuICAgIFxuICAgIGlmIChpbmRleCA9PT0gLTEpIHJldHVybiBudWxsXG4gICAgXG4gICAgZ3JvdXBzW2luZGV4XSA9IHtcbiAgICAgIC4uLmdyb3Vwc1tpbmRleF0sXG4gICAgICAuLi51cGRhdGVzLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuICAgIFxuICAgIHN0b3JhZ2Uuc2F2ZUdyb3Vwcyhncm91cHMpXG4gICAgcmV0dXJuIGdyb3Vwc1tpbmRleF1cbiAgfSxcblxuICBnZXRHcm91cEJ5SWQ6IChpZDogc3RyaW5nKTogR3JvdXAgfCBudWxsID0+IHtcbiAgICBjb25zdCBncm91cHMgPSBzdG9yYWdlLmdldEdyb3VwcygpXG4gICAgcmV0dXJuIGdyb3Vwcy5maW5kKGcgPT4gZy5pZCA9PT0gaWQpIHx8IG51bGxcbiAgfSxcblxuICAvLyDYp9mE2KPYudi22KfYoVxuICBnZXRNZW1iZXJzOiAoKTogTWVtYmVyW10gPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIFtdXG4gICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFNUT1JBR0VfS0VZUy5NRU1CRVJTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZU1lbWJlcnM6IChtZW1iZXJzOiBNZW1iZXJbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLk1FTUJFUlMsIEpTT04uc3RyaW5naWZ5KG1lbWJlcnMpKVxuICB9LFxuXG4gIGdldE1lbWJlcnNCeUdyb3VwSWQ6IChncm91cElkOiBzdHJpbmcpOiBNZW1iZXJbXSA9PiB7XG4gICAgcmV0dXJuIHN0b3JhZ2UuZ2V0TWVtYmVycygpLmZpbHRlcihtID0+IG0uZ3JvdXBfaWQgPT09IGdyb3VwSWQpXG4gIH0sXG5cbiAgYWRkTWVtYmVyOiAobWVtYmVyOiBPbWl0PE1lbWJlciwgJ2lkJyB8ICdqb2luZWRfYXQnPik6IE1lbWJlciA9PiB7XG4gICAgY29uc3QgbmV3TWVtYmVyOiBNZW1iZXIgPSB7XG4gICAgICAuLi5tZW1iZXIsXG4gICAgICBpZDogZ2VuZXJhdGVJZCgpLFxuICAgICAgam9pbmVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gICAgXG4gICAgY29uc3QgbWVtYmVycyA9IHN0b3JhZ2UuZ2V0TWVtYmVycygpXG4gICAgbWVtYmVycy5wdXNoKG5ld01lbWJlcilcbiAgICBzdG9yYWdlLnNhdmVNZW1iZXJzKG1lbWJlcnMpXG4gICAgXG4gICAgcmV0dXJuIG5ld01lbWJlclxuICB9LFxuXG4gIC8vINin2YTZhdiv2YHZiNi52KfYqlxuICBnZXRQYXltZW50czogKCk6IFBheW1lbnRbXSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gW11cbiAgICBjb25zdCBkYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU1RPUkFHRV9LRVlTLlBBWU1FTlRTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZVBheW1lbnRzOiAocGF5bWVudHM6IFBheW1lbnRbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlBBWU1FTlRTLCBKU09OLnN0cmluZ2lmeShwYXltZW50cykpXG4gIH0sXG5cbiAgZ2V0UGF5bWVudHNCeUdyb3VwSWQ6IChncm91cElkOiBzdHJpbmcpOiBQYXltZW50W10gPT4ge1xuICAgIHJldHVybiBzdG9yYWdlLmdldFBheW1lbnRzKCkuZmlsdGVyKHAgPT4gcC5ncm91cF9pZCA9PT0gZ3JvdXBJZClcbiAgfSxcblxuICAvLyDYp9mE2KXYtNi52KfYsdin2KpcbiAgZ2V0Tm90aWZpY2F0aW9uczogKCk6IE5vdGlmaWNhdGlvbltdID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBbXVxuICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWVMuTk9USUZJQ0FUSU9OUylcbiAgICByZXR1cm4gZGF0YSA/IEpTT04ucGFyc2UoZGF0YSkgOiBbXVxuICB9LFxuXG4gIHNhdmVOb3RpZmljYXRpb25zOiAobm90aWZpY2F0aW9uczogTm90aWZpY2F0aW9uW10pID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFNUT1JBR0VfS0VZUy5OT1RJRklDQVRJT05TLCBKU09OLnN0cmluZ2lmeShub3RpZmljYXRpb25zKSlcbiAgfSxcblxuICBhZGROb3RpZmljYXRpb246IChub3RpZmljYXRpb246IE9taXQ8Tm90aWZpY2F0aW9uLCAnaWQnIHwgJ2NyZWF0ZWRfYXQnPik6IE5vdGlmaWNhdGlvbiA9PiB7XG4gICAgY29uc3QgbmV3Tm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24gPSB7XG4gICAgICAuLi5ub3RpZmljYXRpb24sXG4gICAgICBpZDogZ2VuZXJhdGVJZCgpLFxuICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuICAgIFxuICAgIGNvbnN0IG5vdGlmaWNhdGlvbnMgPSBzdG9yYWdlLmdldE5vdGlmaWNhdGlvbnMoKVxuICAgIG5vdGlmaWNhdGlvbnMudW5zaGlmdChuZXdOb3RpZmljYXRpb24pIC8vINil2LbYp9mB2Kkg2YHZiiDYp9mE2YXZgtiv2YXYqVxuICAgIHN0b3JhZ2Uuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucylcbiAgICBcbiAgICByZXR1cm4gbmV3Tm90aWZpY2F0aW9uXG4gIH0sXG5cbiAgLy8g2KfZhNmF2LPYqtiu2K/ZhSDYp9mE2K3Yp9mE2YpcbiAgZ2V0Q3VycmVudFVzZXI6ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsXG4gICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFNUT1JBR0VfS0VZUy5DVVJSRU5UX1VTRVIpXG4gICAgcmV0dXJuIGRhdGEgPyBKU09OLnBhcnNlKGRhdGEpIDogbnVsbFxuICB9LFxuXG4gIHNldEN1cnJlbnRVc2VyOiAodXNlcjogYW55KSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTVE9SQUdFX0tFWVMuQ1VSUkVOVF9VU0VSLCBKU09OLnN0cmluZ2lmeSh1c2VyKSlcbiAgfSxcblxuICAvLyDZhdiz2K0g2KzZhdmK2Lkg2KfZhNio2YrYp9mG2KfYqlxuICBjbGVhckFsbDogKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgT2JqZWN0LnZhbHVlcyhTVE9SQUdFX0tFWVMpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSlcbiAgICB9KVxuICB9XG59XG5cbi8vINiv2KfZhNipINiq2YjZhNmK2K8g2YXYudix2YEg2YHYsdmK2K9cbmZ1bmN0aW9uIGdlbmVyYXRlSWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoMzYpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIpXG59XG5cbi8vINiq2YfZitim2Kkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KrYrNix2YrYqNmK2KlcbmV4cG9ydCBjb25zdCBpbml0aWFsaXplTW9ja0RhdGEgPSAoKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gIFxuICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDYqNmK2KfZhtin2Kog2YXYs9io2YLYqVxuICBjb25zdCBleGlzdGluZ0dyb3VwcyA9IHN0b3JhZ2UuZ2V0R3JvdXBzKClcbiAgaWYgKGV4aXN0aW5nR3JvdXBzLmxlbmd0aCA+IDApIHJldHVyblxuICBcbiAgLy8g2KXZhti02KfYoSDZhdiz2KrYrtiv2YUg2KrYrNix2YrYqNmKXG4gIGNvbnN0IG1vY2tVc2VyID0ge1xuICAgIGlkOiAndXNlcl8xJyxcbiAgICBlbWFpbDogJ3VzZXJAZXhhbXBsZS5jb20nLFxuICAgIGZ1bGxfbmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivJyxcbiAgICBhdmF0YXJfdXJsOiBudWxsXG4gIH1cbiAgc3RvcmFnZS5zZXRDdXJyZW50VXNlcihtb2NrVXNlcilcbiAgXG4gIC8vINil2YbYtNin2KEg2YXYrNmF2YjYudin2Kog2KrYrNix2YrYqNmK2KlcbiAgY29uc3QgbW9ja0dyb3VwczogR3JvdXBbXSA9IFtcbiAgICB7XG4gICAgICBpZDogJ2dyb3VwXzEnLFxuICAgICAgbmFtZTogJ9i12YbYr9mI2YIg2KfZhNij2LXYr9mC2KfYoScsXG4gICAgICBkZXNjcmlwdGlvbjogJ9i12YbYr9mI2YIg2KrYudin2YjZhtmKINmE2YTYo9i12K/Zgtin2KEg2KfZhNmF2YLYsdio2YrZhicsXG4gICAgICBhZG1pbl9pZDogJ3VzZXJfMScsXG4gICAgICBtZW1iZXJfY291bnQ6IDEwLFxuICAgICAgY29udHJpYnV0aW9uX2Ftb3VudDogMTAwMCxcbiAgICAgIGN5Y2xlX2R1cmF0aW9uX21vbnRoczogMTAsXG4gICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgc3RhcnRfZGF0ZTogJzIwMjQtMDEtMDEnLFxuICAgICAgZW5kX2RhdGU6ICcyMDI0LTEwLTMxJyxcbiAgICAgIGRyYXdfY29tcGxldGVkOiB0cnVlLFxuICAgICAgY3VycmVudF9jeWNsZTogMixcbiAgICAgIGNyZWF0ZWRfYXQ6ICcyMDIzLTEyLTE1VDAwOjAwOjAwWicsXG4gICAgICB1cGRhdGVkX2F0OiAnMjAyMy0xMi0xNVQwMDowMDowMFonXG4gICAgfVxuICBdXG4gIFxuICBzdG9yYWdlLnNhdmVHcm91cHMobW9ja0dyb3Vwcylcbn1cbiJdLCJuYW1lcyI6WyJTVE9SQUdFX0tFWVMiLCJHUk9VUFMiLCJNRU1CRVJTIiwiUEFZTUVOVFMiLCJOT1RJRklDQVRJT05TIiwiQ1VSUkVOVF9VU0VSIiwic3RvcmFnZSIsImdldEdyb3VwcyIsImRhdGEiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwic2F2ZUdyb3VwcyIsImdyb3VwcyIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJhZGRHcm91cCIsImdyb3VwIiwibmV3R3JvdXAiLCJpZCIsImdlbmVyYXRlSWQiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlZF9hdCIsInB1c2giLCJ1cGRhdGVHcm91cCIsInVwZGF0ZXMiLCJpbmRleCIsImZpbmRJbmRleCIsImciLCJnZXRHcm91cEJ5SWQiLCJmaW5kIiwiZ2V0TWVtYmVycyIsInNhdmVNZW1iZXJzIiwibWVtYmVycyIsImdldE1lbWJlcnNCeUdyb3VwSWQiLCJncm91cElkIiwiZmlsdGVyIiwibSIsImdyb3VwX2lkIiwiYWRkTWVtYmVyIiwibWVtYmVyIiwibmV3TWVtYmVyIiwiam9pbmVkX2F0IiwiZ2V0UGF5bWVudHMiLCJzYXZlUGF5bWVudHMiLCJwYXltZW50cyIsImdldFBheW1lbnRzQnlHcm91cElkIiwicCIsImdldE5vdGlmaWNhdGlvbnMiLCJzYXZlTm90aWZpY2F0aW9ucyIsIm5vdGlmaWNhdGlvbnMiLCJhZGROb3RpZmljYXRpb24iLCJub3RpZmljYXRpb24iLCJuZXdOb3RpZmljYXRpb24iLCJ1bnNoaWZ0IiwiZ2V0Q3VycmVudFVzZXIiLCJzZXRDdXJyZW50VXNlciIsInVzZXIiLCJjbGVhckFsbCIsIk9iamVjdCIsInZhbHVlcyIsImZvckVhY2giLCJrZXkiLCJyZW1vdmVJdGVtIiwibm93IiwidG9TdHJpbmciLCJNYXRoIiwicmFuZG9tIiwic3Vic3RyIiwiaW5pdGlhbGl6ZU1vY2tEYXRhIiwiZXhpc3RpbmdHcm91cHMiLCJsZW5ndGgiLCJtb2NrVXNlciIsImVtYWlsIiwiZnVsbF9uYW1lIiwiYXZhdGFyX3VybCIsIm1vY2tHcm91cHMiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJhZG1pbl9pZCIsIm1lbWJlcl9jb3VudCIsImNvbnRyaWJ1dGlvbl9hbW91bnQiLCJjeWNsZV9kdXJhdGlvbl9tb250aHMiLCJzdGF0dXMiLCJzdGFydF9kYXRlIiwiZW5kX2RhdGUiLCJkcmF3X2NvbXBsZXRlZCIsImN1cnJlbnRfY3ljbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/storage.ts\n"));

/***/ })

});