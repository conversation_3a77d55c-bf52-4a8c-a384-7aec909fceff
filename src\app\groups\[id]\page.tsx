'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Users, DollarSign, Calendar, Settings, Shuffle, CheckCircle, Clock, AlertCircle } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'
import Button from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { formatCurrency, formatDate, getStatusColor, getStatusText } from '@/lib/utils'

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'أحمد محمد',
  avatar_url: null
}

const mockGroup = {
  id: '1',
  name: 'صندوق الأصدقاء',
  description: 'صندوق تعاوني للأصدقاء المقربين',
  admin_id: '1',
  member_count: 10,
  contribution_amount: 1000,
  cycle_duration_months: 10,
  status: 'active',
  start_date: '2024-01-01',
  end_date: '2024-10-31',
  draw_completed: true,
  current_cycle: 2,
  created_at: '2023-12-15'
}

const mockMembers = [
  { id: '1', user_id: '1', full_name: 'أحمد محمد', position_in_draw: 3, is_admin: true, status: 'active' },
  { id: '2', user_id: '2', full_name: 'محمد علي', position_in_draw: 1, is_admin: false, status: 'active' },
  { id: '3', user_id: '3', full_name: 'سارة أحمد', position_in_draw: 2, is_admin: false, status: 'active' },
  { id: '4', user_id: '4', full_name: 'فاطمة محمد', position_in_draw: 4, is_admin: false, status: 'active' },
  { id: '5', user_id: '5', full_name: 'عبدالله سالم', position_in_draw: 5, is_admin: false, status: 'active' },
]

const mockPayments = [
  { id: '1', member_name: 'أحمد محمد', cycle_number: 2, amount: 1000, status: 'paid', paid_date: '2024-02-01' },
  { id: '2', member_name: 'محمد علي', cycle_number: 2, amount: 1000, status: 'paid', paid_date: '2024-02-02' },
  { id: '3', member_name: 'سارة أحمد', cycle_number: 2, amount: 1000, status: 'pending', due_date: '2024-02-15' },
  { id: '4', member_name: 'فاطمة محمد', cycle_number: 2, amount: 1000, status: 'overdue', due_date: '2024-02-10' },
]

export default function GroupDetailsPage({ params }: { params: { id: string } }) {
  const [group, setGroup] = useState(mockGroup)
  const [members, setMembers] = useState(mockMembers)
  const [payments, setPayments] = useState(mockPayments)
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'payments'>('overview')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const isAdmin = group.admin_id === mockUser.id
  const totalCollected = payments.filter(p => p.status === 'paid').length * group.contribution_amount
  const pendingPayments = payments.filter(p => p.status === 'pending').length
  const overduePayments = payments.filter(p => p.status === 'overdue').length

  useEffect(() => {
    // TODO: Fetch actual data from Supabase
    setLoading(false)
  }, [params.id])

  const handleDrawLottery = async () => {
    setLoading(true)
    try {
      // TODO: Implement lottery draw logic
      console.log('Drawing lottery for group:', params.id)
      await new Promise(resolve => setTimeout(resolve, 1000))
      // Update group state
    } catch (error) {
      console.error('Lottery draw error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar user={mockUser} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 ml-2" />
            العودة
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {group.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                {group.description}
              </p>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <Badge
                variant={
                  group.status === 'active' ? 'success' :
                  group.status === 'draft' ? 'warning' : 'default'
                }
              >
                {getStatusText(group.status)}
              </Badge>
              
              {isAdmin && (
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 ml-2" />
                  إعدادات
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Users className="w-6 h-6 text-primary-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    الأعضاء
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {group.member_count}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    المبلغ المجمع
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(totalCollected)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    مدفوعات معلقة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {pendingPayments}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <AlertCircle className="w-6 h-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    متأخرة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {overduePayments}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8 space-x-reverse">
              {[
                { id: 'overview', name: 'نظرة عامة', icon: Calendar },
                { id: 'members', name: 'الأعضاء', icon: Users },
                { id: 'payments', name: 'المدفوعات', icon: DollarSign },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 ml-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>معلومات المجموعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">المساهمة الشهرية:</span>
                  <span className="font-medium">{formatCurrency(group.contribution_amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">مدة الدورة:</span>
                  <span className="font-medium">{group.cycle_duration_months} شهر</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">تاريخ البداية:</span>
                  <span className="font-medium">{formatDate(group.start_date!)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">تاريخ النهاية:</span>
                  <span className="font-medium">{formatDate(group.end_date!)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">الدورة الحالية:</span>
                  <span className="font-medium">{group.current_cycle} من {group.cycle_duration_months}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>القرعة</CardTitle>
                <CardDescription>
                  ترتيب الصرف للأعضاء
                </CardDescription>
              </CardHeader>
              <CardContent>
                {group.draw_completed ? (
                  <div className="space-y-3">
                    {members
                      .sort((a, b) => (a.position_in_draw || 0) - (b.position_in_draw || 0))
                      .map((member) => (
                        <div
                          key={member.id}
                          className={`flex items-center justify-between p-3 rounded-lg ${
                            member.position_in_draw === group.current_cycle
                              ? 'bg-primary-50 border border-primary-200'
                              : 'bg-gray-50 dark:bg-gray-800'
                          }`}
                        >
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                              member.position_in_draw === group.current_cycle
                                ? 'bg-primary-600 text-white'
                                : 'bg-gray-200 text-gray-700'
                            }`}>
                              {member.position_in_draw}
                            </div>
                            <span className="mr-3 font-medium">{member.full_name}</span>
                            {member.is_admin && (
                              <Badge variant="info" className="mr-2">مسؤول</Badge>
                            )}
                          </div>
                          {member.position_in_draw! < group.current_cycle && (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          )}
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Shuffle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      لم يتم إجراء القرعة بعد
                    </p>
                    {isAdmin && (
                      <Button onClick={handleDrawLottery} loading={loading}>
                        <Shuffle className="w-4 h-4 ml-2" />
                        إجراء القرعة
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'members' && (
          <Card>
            <CardHeader>
              <CardTitle>أعضاء المجموعة</CardTitle>
              <CardDescription>
                قائمة بجميع أعضاء الصندوق التعاوني
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {members.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-medium">
                          {member.full_name.charAt(0)}
                        </span>
                      </div>
                      <div className="mr-4">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {member.full_name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {member.position_in_draw ? `الترتيب: ${member.position_in_draw}` : 'لم يتم تحديد الترتيب'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {member.is_admin && (
                        <Badge variant="info">مسؤول</Badge>
                      )}
                      <Badge
                        variant={member.status === 'active' ? 'success' : 'default'}
                      >
                        {getStatusText(member.status)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'payments' && (
          <Card>
            <CardHeader>
              <CardTitle>مدفوعات الدورة الحالية</CardTitle>
              <CardDescription>
                حالة المدفوعات للدورة رقم {group.current_cycle}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {payment.member_name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {payment.status === 'paid' && payment.paid_date
                          ? `تم الدفع في ${formatDate(payment.paid_date)}`
                          : payment.status === 'pending'
                          ? `مستحق في ${formatDate(payment.due_date!)}`
                          : `متأخر منذ ${formatDate(payment.due_date!)}`
                        }
                      </p>
                    </div>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <span className="font-medium">
                        {formatCurrency(payment.amount)}
                      </span>
                      <Badge
                        variant={
                          payment.status === 'paid' ? 'success' :
                          payment.status === 'pending' ? 'warning' : 'danger'
                        }
                      >
                        {getStatusText(payment.status)}
                      </Badge>
                      {isAdmin && payment.status !== 'paid' && (
                        <Button size="sm" variant="outline">
                          تأكيد الدفع
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
