'use client'

import { useState, useEffect } from 'react'
import { Bell, DollarSign, Users, Calendar, CheckCircle, X, Filter } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'
import Button from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { formatDate, formatCurrency } from '@/lib/utils'
import { storage } from '@/lib/storage'

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'أحمد محمد',
  avatar_url: null
}

const mockNotifications = [
  {
    id: '1',
    title: 'مساهمة مستحقة',
    message: 'مساهمتك الشهرية لصندوق الأصدقاء مستحقة اليوم',
    type: 'payment_due',
    group_name: 'صندوق الأصدقاء',
    amount: 500,
    read: false,
    created_at: '2024-02-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'تم استلام مساهمة',
    message: 'تم استلام مساهمة محمد علي لصندوق الأصدقاء',
    type: 'payment_received',
    group_name: 'صندوق الأصدقاء',
    amount: 500,
    read: false,
    created_at: '2024-02-14T15:30:00Z'
  },
  {
    id: '3',
    title: 'موعد الصرف',
    message: 'سيتم صرف مبلغ 5,000 جنيه لسارة أحمد غداً',
    type: 'disbursement',
    group_name: 'صندوق الأصدقاء',
    amount: 5000,
    read: true,
    created_at: '2024-02-13T09:00:00Z'
  },
  {
    id: '4',
    title: 'عضو جديد',
    message: 'انضم عبدالله سالم إلى صندوق العائلة',
    type: 'general',
    group_name: 'صندوق العائلة',
    read: true,
    created_at: '2024-02-12T14:20:00Z'
  },
  {
    id: '5',
    title: 'تم إجراء القرعة',
    message: 'تم إجراء قرعة صندوق الأصدقاء وتحديد ترتيب الأعضاء',
    type: 'general',
    group_name: 'صندوق الأصدقاء',
    read: true,
    created_at: '2024-01-15T11:00:00Z'
  }
]

type NotificationType = 'all' | 'payment_due' | 'payment_received' | 'disbursement' | 'general'

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<any[]>([])
  const [user, setUser] = useState<any>(null)
  const [filter, setFilter] = useState<NotificationType>('all')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // تحميل البيانات من التخزين المحلي
    const currentUser = storage.getCurrentUser()
    const allNotifications = storage.getNotifications()

    setUser(currentUser)
    setNotifications(allNotifications)
  }, [])

  const filteredNotifications = notifications.filter(notification => 
    filter === 'all' || notification.type === filter
  )

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = async (notificationId: string) => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    )
    setNotifications(updatedNotifications)
    storage.saveNotifications(updatedNotifications)
  }

  const markAllAsRead = async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      const updatedNotifications = notifications.map(notification => ({ ...notification, read: true }))
      setNotifications(updatedNotifications)
      storage.saveNotifications(updatedNotifications)
    } catch (error) {
      console.error('Error marking all as read:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteNotification = async (notificationId: string) => {
    const updatedNotifications = notifications.filter(notification => notification.id !== notificationId)
    setNotifications(updatedNotifications)
    storage.saveNotifications(updatedNotifications)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'payment_due':
      case 'payment_received':
        return <DollarSign className="w-5 h-5" />
      case 'disbursement':
        return <Calendar className="w-5 h-5" />
      case 'general':
        return <Users className="w-5 h-5" />
      default:
        return <Bell className="w-5 h-5" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'payment_due':
        return 'text-red-600 bg-red-100'
      case 'payment_received':
        return 'text-green-600 bg-green-100'
      case 'disbursement':
        return 'text-blue-600 bg-blue-100'
      case 'general':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'payment_due':
        return 'مساهمة مستحقة'
      case 'payment_received':
        return 'مساهمة مستلمة'
      case 'disbursement':
        return 'صرف'
      case 'general':
        return 'عام'
      default:
        return type
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar user={user} />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              الإشعارات
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              {unreadCount > 0 ? `لديك ${unreadCount} إشعار غير مقروء` : 'جميع الإشعارات مقروءة'}
            </p>
          </div>
          
          {unreadCount > 0 && (
            <Button
              onClick={markAllAsRead}
              loading={loading}
              variant="outline"
            >
              <CheckCircle className="w-4 h-4 ml-2" />
              تحديد الكل كمقروء
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 ml-2" />
              تصفية الإشعارات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: 'الكل' },
                { key: 'payment_due', label: 'مساهمات مستحقة' },
                { key: 'payment_received', label: 'مساهمات مستلمة' },
                { key: 'disbursement', label: 'صرف' },
                { key: 'general', label: 'عام' }
              ].map((filterOption) => (
                <button
                  key={filterOption.key}
                  onClick={() => setFilter(filterOption.key as NotificationType)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === filterOption.key
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                >
                  {filterOption.label}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <div className="space-y-4">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map((notification) => (
              <Card
                key={notification.id}
                className={`transition-all hover:shadow-md ${
                  !notification.read ? 'border-primary-200 bg-primary-50/30 dark:bg-primary-900/10' : ''
                }`}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 space-x-reverse flex-1">
                      {/* Icon */}
                      <div className={`p-2 rounded-full ${getNotificationColor(notification.type)}`}>
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className={`font-semibold ${
                            !notification.read ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {notification.title}
                          </h3>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                          )}
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-400 mb-2">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                            <span>{notification.group_name}</span>
                            <span>{formatDate(notification.created_at)}</span>
                            {notification.amount && (
                              <span className="font-medium text-primary-600">
                                {formatCurrency(notification.amount)}
                              </span>
                            )}
                          </div>
                          
                          <Badge variant="default">
                            {getTypeText(notification.type)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2 space-x-reverse mr-4">
                      {!notification.read && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="p-1 text-gray-400 hover:text-primary-600 transition-colors"
                          title="تحديد كمقروء"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="حذف الإشعار"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  لا توجد إشعارات
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {filter === 'all' 
                    ? 'لا توجد إشعارات حالياً'
                    : `لا توجد إشعارات من نوع "${getTypeText(filter)}"`
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
