'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Users, DollarSign, Calendar, Search, Filter, MoreVertical, Setting<PERSON>, Eye } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { formatCurrency, formatDate, getStatusText } from '@/lib/utils'
import { storage, initializeMockData } from '@/lib/storage'

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'أحمد محمد',
  avatar_url: null
}

const mockGroups = [
  {
    id: '1',
    name: 'صندوق الأصدقاء',
    description: 'صندوق تعاوني للأصدقاء المقربين',
    admin_id: '1',
    member_count: 10,
    contribution_amount: 500,
    cycle_duration_months: 10,
    status: 'active',
    start_date: '2024-01-01',
    end_date: '2024-10-31',
    current_cycle: 2,
    my_position: 3,
    next_disbursement: '2024-03-01',
    total_collected: 20000,
    created_at: '2023-12-15'
  },
  {
    id: '2',
    name: 'صندوق العائلة',
    description: 'صندوق تعاوني لأفراد العائلة',
    admin_id: '2',
    member_count: 8,
    contribution_amount: 800,
    cycle_duration_months: 8,
    status: 'draft',
    start_date: null,
    end_date: null,
    current_cycle: 0,
    my_position: null,
    next_disbursement: null,
    total_collected: 0,
    created_at: '2024-02-01'
  },
  {
    id: '3',
    name: 'صندوق العمل',
    description: 'صندوق تعاوني لزملاء العمل',
    admin_id: '1',
    member_count: 12,
    contribution_amount: 600,
    cycle_duration_months: 12,
    status: 'completed',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    current_cycle: 12,
    my_position: 5,
    next_disbursement: null,
    total_collected: 86400,
    created_at: '2022-12-01'
  }
]

type GroupStatus = 'all' | 'draft' | 'active' | 'completed' | 'cancelled'

export default function GroupsPage() {
  const [groups, setGroups] = useState<any[]>([])
  const [user, setUser] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<GroupStatus>('all')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // تهيئة البيانات التجريبية
    initializeMockData()

    // تحميل البيانات
    const currentUser = storage.getCurrentUser()
    const allGroups = storage.getGroups()

    setUser(currentUser)
    setGroups(allGroups)
    setLoading(false)
  }, [])

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         group.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || group.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'draft':
        return 'warning'
      case 'completed':
        return 'info'
      case 'cancelled':
        return 'danger'
      default:
        return 'default'
    }
  }

  const isAdmin = (group: any) => group.admin_id === user?.id

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navbar user={user} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">جاري التحميل...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar user={user} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              مجموعاتي
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              إدارة الصناديق التعاونية التي تشارك فيها
            </p>
          </div>
          
          <Link href="/groups/create">
            <Button>
              <Plus className="w-4 h-4 ml-2" />
              إنشاء مجموعة جديدة
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="البحث في المجموعات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              
              {/* Status Filter */}
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as GroupStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="draft">مسودة</option>
                  <option value="active">نشط</option>
                  <option value="completed">مكتمل</option>
                  <option value="cancelled">ملغي</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Groups Grid */}
        {filteredGroups.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGroups.map((group) => (
              <Card key={group.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-2">{group.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {group.description}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Badge variant={getStatusVariant(group.status)}>
                        {getStatusText(group.status)}
                      </Badge>
                      {isAdmin(group) && (
                        <Badge variant="info">مسؤول</Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <Users className="w-5 h-5 text-primary-600 mx-auto mb-1" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">الأعضاء</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {group.member_count}
                      </p>
                    </div>
                    
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <DollarSign className="w-5 h-5 text-green-600 mx-auto mb-1" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">المساهمة</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(group.contribution_amount)}
                      </p>
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="space-y-2 mb-4">
                    {group.status === 'active' && (
                      <>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">الدورة الحالية:</span>
                          <span className="font-medium">{group.current_cycle} من {group.cycle_duration_months}</span>
                        </div>
                        {group.my_position && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">ترتيبي:</span>
                            <span className="font-medium">{group.my_position}</span>
                          </div>
                        )}
                        {group.next_disbursement && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">الصرف التالي:</span>
                            <span className="font-medium text-primary-600">
                              {formatDate(group.next_disbursement)}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                    
                    {group.status === 'completed' && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">إجمالي المجمع:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(group.total_collected)}
                        </span>
                      </div>
                    )}
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                      <span className="font-medium">{formatDate(group.created_at)}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/groups/${group.id}`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        <Eye className="w-4 h-4 ml-2" />
                        عرض التفاصيل
                      </Button>
                    </Link>
                    
                    {isAdmin(group) && (
                      <Button variant="ghost" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchTerm || statusFilter !== 'all' 
                  ? 'لا توجد مجموعات تطابق البحث'
                  : 'لا توجد مجموعات'
                }
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {searchTerm || statusFilter !== 'all'
                  ? 'جرب تغيير معايير البحث أو الفلتر'
                  : 'ابدأ بإنشاء مجموعة جديدة للصندوق التعاوني'
                }
              </p>
              
              {!searchTerm && statusFilter === 'all' && (
                <Link href="/groups/create">
                  <Button>
                    <Plus className="w-4 h-4 ml-2" />
                    إنشاء مجموعة جديدة
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
