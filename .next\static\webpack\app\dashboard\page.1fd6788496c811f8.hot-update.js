"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,DollarSign,Plus,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data - replace with actual data from Supabase\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroups = [\n    {\n        id: \"1\",\n        name: \"صندوق الأصدقاء\",\n        member_count: 10,\n        contribution_amount: 1000,\n        status: \"active\",\n        next_disbursement: \"2024-02-15\",\n        my_position: 3,\n        current_cycle: 2\n    },\n    {\n        id: \"2\",\n        name: \"صندوق العائلة\",\n        member_count: 8,\n        contribution_amount: 2000,\n        status: \"draft\",\n        next_disbursement: null,\n        my_position: null,\n        current_cycle: 0\n    }\n];\nconst mockStats = {\n    total_groups: 2,\n    active_groups: 1,\n    total_contributed: 3000,\n    total_received: 0,\n    pending_payments: 1000,\n    next_disbursement: 16000\n};\nconst mockRecentActivity = [\n    {\n        id: \"1\",\n        type: \"payment\",\n        message: \"تم دفع مساهمة شهر فبراير لصندوق الأصدقاء\",\n        date: \"2024-02-01\",\n        amount: 1000\n    },\n    {\n        id: \"2\",\n        type: \"disbursement\",\n        message: \"تم صرف مبلغ لمحمد علي من صندوق الأصدقاء\",\n        date: \"2024-01-15\",\n        amount: 10000\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تهيئة البيانات التجريبية\n        (0,_lib_storage__WEBPACK_IMPORTED_MODULE_8__.initializeMockData)();\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getCurrentUser();\n        const allGroups = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getGroups();\n        const notifications = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getNotifications();\n        // حساب الإحصائيات\n        const activeGroups = allGroups.filter((g)=>g.status === \"active\").length;\n        const totalContributed = allGroups.reduce((sum, group)=>{\n            return sum + group.contribution_amount * group.current_cycle;\n        }, 0);\n        const calculatedStats = {\n            total_groups: allGroups.length,\n            active_groups: activeGroups,\n            total_contributed: totalContributed,\n            total_received: 0,\n            pending_payments: 1000,\n            next_disbursement: 16000\n        };\n        // تحويل الإشعارات إلى أنشطة حديثة\n        const activities = notifications.slice(0, 5).map((notif)=>({\n                id: notif.id,\n                type: notif.type === \"payment_due\" ? \"payment\" : \"disbursement\",\n                message: notif.message,\n                date: notif.created_at,\n                amount: notif.amount || 0\n            }));\n        setUser(currentUser);\n        setGroups(allGroups);\n        setStats(calculatedStats);\n        setRecentActivity(activities);\n        setLoading(false);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: [\n                                    \"مرحباً، \",\n                                    user.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                children: \"إليك نظرة عامة على صناديقك التعاونية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"إجمالي المجموعات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: stats.total_groups\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المجموعات النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: stats.active_groups\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"إجمالي المساهمات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(stats.total_contributed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(stats.pending_payments)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                            children: \"مجموعاتي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                            children: \"الصناديق التعاونية التي تشارك فيها\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/groups/create\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"إنشاء مجموعة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                                    children: group.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 234,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                    variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 237,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        group.member_count,\n                                                                                        \" أعضاء\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount),\n                                                                                        \" شهرياً\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                group.my_position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"ترتيبي: \",\n                                                                                        group.my_position\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 250,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        group.next_disbursement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-primary-600 mt-1\",\n                                                                            children: [\n                                                                                \"الصرف التالي: \",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.next_disbursement)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/groups/\".concat(group.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"عرض التفاصيل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, group.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    groups.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                                children: \"لم تنضم إلى أي مجموعة بعد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/groups/create\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4 ml-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"إنشاء مجموعة جديدة\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"النشاط الأخير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                    children: \"آخر المعاملات والأنشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 rounded-full \".concat(activity.type === \"payment\" ? \"bg-green-100\" : \"bg-blue-100\"),\n                                                                    children: activity.type === \"payment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_DollarSign_Plus_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-900 dark:text-white\",\n                                                                            children: activity.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(activity.date)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(activity.amount)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 316,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, activity.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    recentActivity.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                            children: \"لا توجد أنشطة حديثة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"P3bM4FYgRKHl2XsYF4CP9Q/lqfo=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeMockData: function() { return /* binding */ initializeMockData; },\n/* harmony export */   storage: function() { return /* binding */ storage; }\n/* harmony export */ });\n// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    GROUPS: \"sarfa_groups\",\n    MEMBERS: \"sarfa_members\",\n    PAYMENTS: \"sarfa_payments\",\n    NOTIFICATIONS: \"sarfa_notifications\",\n    CURRENT_USER: \"sarfa_current_user\"\n};\n// دوال مساعدة للتخزين المحلي\nconst storage = {\n    // المجموعات\n    getGroups: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.GROUPS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveGroups: (groups)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups));\n    },\n    addGroup: (group)=>{\n        const newGroup = {\n            ...group,\n            id: generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const groups = storage.getGroups();\n        groups.push(newGroup);\n        storage.saveGroups(groups);\n        return newGroup;\n    },\n    updateGroup: (id, updates)=>{\n        const groups = storage.getGroups();\n        const index = groups.findIndex((g)=>g.id === id);\n        if (index === -1) return null;\n        groups[index] = {\n            ...groups[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        storage.saveGroups(groups);\n        return groups[index];\n    },\n    getGroupById: (id)=>{\n        const groups = storage.getGroups();\n        return groups.find((g)=>g.id === id) || null;\n    },\n    // الأعضاء\n    getMembers: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveMembers: (members)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));\n    },\n    getMembersByGroupId: (groupId)=>{\n        return storage.getMembers().filter((m)=>m.group_id === groupId);\n    },\n    addMember: (member)=>{\n        const newMember = {\n            ...member,\n            id: generateId(),\n            joined_at: new Date().toISOString()\n        };\n        const members = storage.getMembers();\n        members.push(newMember);\n        storage.saveMembers(members);\n        return newMember;\n    },\n    // المدفوعات\n    getPayments: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS);\n        return data ? JSON.parse(data) : [];\n    },\n    savePayments: (payments)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments));\n    },\n    getPaymentsByGroupId: (groupId)=>{\n        return storage.getPayments().filter((p)=>p.group_id === groupId);\n    },\n    // الإشعارات\n    getNotifications: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveNotifications: (notifications)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    },\n    addNotification: (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: generateId(),\n            created_at: new Date().toISOString()\n        };\n        const notifications = storage.getNotifications();\n        notifications.unshift(newNotification) // إضافة في المقدمة\n        ;\n        storage.saveNotifications(notifications);\n        return newNotification;\n    },\n    // المستخدم الحالي\n    getCurrentUser: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n        return data ? JSON.parse(data) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));\n    },\n    // مسح جميع البيانات\n    clearAll: ()=>{\n        if (false) {}\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n    }\n};\n// دالة توليد معرف فريد\nfunction generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n// تهيئة البيانات التجريبية\nconst initializeMockData = ()=>{\n    if (false) {}\n    // التحقق من وجود بيانات مسبقة\n    const existingGroups = storage.getGroups();\n    if (existingGroups.length > 0) return;\n    // إنشاء مستخدم تجريبي\n    const mockUser = {\n        id: \"user_1\",\n        email: \"<EMAIL>\",\n        full_name: \"أحمد محمد\",\n        avatar_url: null\n    };\n    storage.setCurrentUser(mockUser);\n    // إنشاء مجموعات تجريبية\n    const mockGroups = [\n        {\n            id: \"group_1\",\n            name: \"صندوق الأصدقاء\",\n            description: \"صندوق تعاوني للأصدقاء المقربين\",\n            admin_id: \"user_1\",\n            member_count: 10,\n            contribution_amount: 1000,\n            cycle_duration_months: 10,\n            status: \"active\",\n            start_date: \"2024-01-01\",\n            end_date: \"2024-10-31\",\n            draw_completed: true,\n            current_cycle: 2,\n            created_at: \"2023-12-15T00:00:00Z\",\n            updated_at: \"2023-12-15T00:00:00Z\"\n        }\n    ];\n    storage.saveGroups(mockGroups);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3RvcmFnZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHVEQUF1RDtBQTJEdkQsd0JBQXdCO0FBQ3hCLE1BQU1BLGVBQWU7SUFDbkJDLFFBQVE7SUFDUkMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLGVBQWU7SUFDZkMsY0FBYztBQUNoQjtBQUVBLDZCQUE2QjtBQUN0QixNQUFNQyxVQUFVO0lBQ3JCLFlBQVk7SUFDWkMsV0FBVztRQUNULElBQUksS0FBa0IsRUFBYSxFQUFTO1FBQzVDLE1BQU1DLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUMsTUFBTTtRQUNyRCxPQUFPTyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBSyxZQUFZLENBQUNDO1FBQ1gsSUFBSSxLQUFrQixFQUFhO1FBQ25DTCxhQUFhTSxPQUFPLENBQUNmLGFBQWFDLE1BQU0sRUFBRVUsS0FBS0ssU0FBUyxDQUFDRjtJQUMzRDtJQUVBRyxVQUFVLENBQUNDO1FBQ1QsTUFBTUMsV0FBa0I7WUFDdEIsR0FBR0QsS0FBSztZQUNSRSxJQUFJQztZQUNKQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7WUFDbENDLFlBQVksSUFBSUYsT0FBT0MsV0FBVztRQUNwQztRQUVBLE1BQU1WLFNBQVNSLFFBQVFDLFNBQVM7UUFDaENPLE9BQU9ZLElBQUksQ0FBQ1A7UUFDWmIsUUFBUU8sVUFBVSxDQUFDQztRQUVuQixPQUFPSztJQUNUO0lBRUFRLGFBQWEsQ0FBQ1AsSUFBWVE7UUFDeEIsTUFBTWQsU0FBU1IsUUFBUUMsU0FBUztRQUNoQyxNQUFNc0IsUUFBUWYsT0FBT2dCLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVgsRUFBRSxLQUFLQTtRQUU3QyxJQUFJUyxVQUFVLENBQUMsR0FBRyxPQUFPO1FBRXpCZixNQUFNLENBQUNlLE1BQU0sR0FBRztZQUNkLEdBQUdmLE1BQU0sQ0FBQ2UsTUFBTTtZQUNoQixHQUFHRCxPQUFPO1lBQ1ZILFlBQVksSUFBSUYsT0FBT0MsV0FBVztRQUNwQztRQUVBbEIsUUFBUU8sVUFBVSxDQUFDQztRQUNuQixPQUFPQSxNQUFNLENBQUNlLE1BQU07SUFDdEI7SUFFQUcsY0FBYyxDQUFDWjtRQUNiLE1BQU1OLFNBQVNSLFFBQVFDLFNBQVM7UUFDaEMsT0FBT08sT0FBT21CLElBQUksQ0FBQ0YsQ0FBQUEsSUFBS0EsRUFBRVgsRUFBRSxLQUFLQSxPQUFPO0lBQzFDO0lBRUEsVUFBVTtJQUNWYyxZQUFZO1FBQ1YsSUFBSSxLQUFrQixFQUFhLEVBQVM7UUFDNUMsTUFBTTFCLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUUsT0FBTztRQUN0RCxPQUFPTSxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBMkIsYUFBYSxDQUFDQztRQUNaLElBQUksS0FBa0IsRUFBYTtRQUNuQzNCLGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUUsT0FBTyxFQUFFUyxLQUFLSyxTQUFTLENBQUNvQjtJQUM1RDtJQUVBQyxxQkFBcUIsQ0FBQ0M7UUFDcEIsT0FBT2hDLFFBQVE0QixVQUFVLEdBQUdLLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxLQUFLSDtJQUN6RDtJQUVBSSxXQUFXLENBQUNDO1FBQ1YsTUFBTUMsWUFBb0I7WUFDeEIsR0FBR0QsTUFBTTtZQUNUdkIsSUFBSUM7WUFDSndCLFdBQVcsSUFBSXRCLE9BQU9DLFdBQVc7UUFDbkM7UUFFQSxNQUFNWSxVQUFVOUIsUUFBUTRCLFVBQVU7UUFDbENFLFFBQVFWLElBQUksQ0FBQ2tCO1FBQ2J0QyxRQUFRNkIsV0FBVyxDQUFDQztRQUVwQixPQUFPUTtJQUNUO0lBRUEsWUFBWTtJQUNaRSxhQUFhO1FBQ1gsSUFBSSxLQUFrQixFQUFhLEVBQVM7UUFDNUMsTUFBTXRDLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUcsUUFBUTtRQUN2RCxPQUFPSyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVEsRUFBRTtJQUNyQztJQUVBdUMsY0FBYyxDQUFDQztRQUNiLElBQUksS0FBa0IsRUFBYTtRQUNuQ3ZDLGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUcsUUFBUSxFQUFFUSxLQUFLSyxTQUFTLENBQUNnQztJQUM3RDtJQUVBQyxzQkFBc0IsQ0FBQ1g7UUFDckIsT0FBT2hDLFFBQVF3QyxXQUFXLEdBQUdQLE1BQU0sQ0FBQ1csQ0FBQUEsSUFBS0EsRUFBRVQsUUFBUSxLQUFLSDtJQUMxRDtJQUVBLFlBQVk7SUFDWmEsa0JBQWtCO1FBQ2hCLElBQUksS0FBa0IsRUFBYSxFQUFTO1FBQzVDLE1BQU0zQyxPQUFPQyxhQUFhQyxPQUFPLENBQUNWLGFBQWFJLGFBQWE7UUFDNUQsT0FBT0ksT0FBT0csS0FBS0MsS0FBSyxDQUFDSixRQUFRLEVBQUU7SUFDckM7SUFFQTRDLG1CQUFtQixDQUFDQztRQUNsQixJQUFJLEtBQWtCLEVBQWE7UUFDbkM1QyxhQUFhTSxPQUFPLENBQUNmLGFBQWFJLGFBQWEsRUFBRU8sS0FBS0ssU0FBUyxDQUFDcUM7SUFDbEU7SUFFQUMsaUJBQWlCLENBQUNDO1FBQ2hCLE1BQU1DLGtCQUFnQztZQUNwQyxHQUFHRCxZQUFZO1lBQ2ZuQyxJQUFJQztZQUNKQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7UUFDcEM7UUFFQSxNQUFNNkIsZ0JBQWdCL0MsUUFBUTZDLGdCQUFnQjtRQUM5Q0UsY0FBY0ksT0FBTyxDQUFDRCxpQkFBaUIsbUJBQW1COztRQUMxRGxELFFBQVE4QyxpQkFBaUIsQ0FBQ0M7UUFFMUIsT0FBT0c7SUFDVDtJQUVBLGtCQUFrQjtJQUNsQkUsZ0JBQWdCO1FBQ2QsSUFBSSxLQUFrQixFQUFhLEVBQU87UUFDMUMsTUFBTWxELE9BQU9DLGFBQWFDLE9BQU8sQ0FBQ1YsYUFBYUssWUFBWTtRQUMzRCxPQUFPRyxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVE7SUFDbkM7SUFFQW1ELGdCQUFnQixDQUFDQztRQUNmLElBQUksS0FBa0IsRUFBYTtRQUNuQ25ELGFBQWFNLE9BQU8sQ0FBQ2YsYUFBYUssWUFBWSxFQUFFTSxLQUFLSyxTQUFTLENBQUM0QztJQUNqRTtJQUVBLG9CQUFvQjtJQUNwQkMsVUFBVTtRQUNSLElBQUksS0FBa0IsRUFBYTtRQUNuQ0MsT0FBT0MsTUFBTSxDQUFDL0QsY0FBY2dFLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDbEN4RCxhQUFheUQsVUFBVSxDQUFDRDtRQUMxQjtJQUNGO0FBQ0YsRUFBQztBQUVELHVCQUF1QjtBQUN2QixTQUFTNUM7SUFDUCxPQUFPRSxLQUFLNEMsR0FBRyxHQUFHQyxRQUFRLENBQUMsTUFBTUMsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsTUFBTSxDQUFDO0FBQ3JFO0FBRUEsMkJBQTJCO0FBQ3BCLE1BQU1DLHFCQUFxQjtJQUNoQyxJQUFJLEtBQWtCLEVBQWE7SUFFbkMsOEJBQThCO0lBQzlCLE1BQU1DLGlCQUFpQm5FLFFBQVFDLFNBQVM7SUFDeEMsSUFBSWtFLGVBQWVDLE1BQU0sR0FBRyxHQUFHO0lBRS9CLHNCQUFzQjtJQUN0QixNQUFNQyxXQUFXO1FBQ2Z2RCxJQUFJO1FBQ0p3RCxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsWUFBWTtJQUNkO0lBQ0F4RSxRQUFRcUQsY0FBYyxDQUFDZ0I7SUFFdkIsd0JBQXdCO0lBQ3hCLE1BQU1JLGFBQXNCO1FBQzFCO1lBQ0UzRCxJQUFJO1lBQ0o0RCxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLHFCQUFxQjtZQUNyQkMsdUJBQXVCO1lBQ3ZCQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWkMsVUFBVTtZQUNWQyxnQkFBZ0I7WUFDaEJDLGVBQWU7WUFDZnBFLFlBQVk7WUFDWkcsWUFBWTtRQUNkO0tBQ0Q7SUFFRG5CLFFBQVFPLFVBQVUsQ0FBQ2tFO0FBQ3JCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9zdG9yYWdlLnRzP2FmZTgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g2YbYuNin2YUg2KrYrtiy2YrZhiDZhdit2YTZiiDZhNmE2KjZitin2YbYp9iqICjZhdik2YLYqiDYrdiq2Ykg2YrYqtmFINix2KjYtyBTdXBhYmFzZSlcblxuZXhwb3J0IGludGVyZmFjZSBHcm91cCB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsXG4gIGFkbWluX2lkOiBzdHJpbmdcbiAgbWVtYmVyX2NvdW50OiBudW1iZXJcbiAgY29udHJpYnV0aW9uX2Ftb3VudDogbnVtYmVyXG4gIGN5Y2xlX2R1cmF0aW9uX21vbnRoczogbnVtYmVyXG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdhY3RpdmUnIHwgJ2NvbXBsZXRlZCcgfCAnY2FuY2VsbGVkJ1xuICBzdGFydF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIGVuZF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIGRyYXdfY29tcGxldGVkOiBib29sZWFuXG4gIGN1cnJlbnRfY3ljbGU6IG51bWJlclxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVtYmVyIHtcbiAgaWQ6IHN0cmluZ1xuICBncm91cF9pZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICBwb3NpdGlvbl9pbl9kcmF3OiBudW1iZXIgfCBudWxsXG4gIGlzX2FkbWluOiBib29sZWFuXG4gIGpvaW5lZF9hdDogc3RyaW5nXG4gIHN0YXR1czogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnIHwgJ3JlbW92ZWQnXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGF5bWVudCB7XG4gIGlkOiBzdHJpbmdcbiAgZ3JvdXBfaWQ6IHN0cmluZ1xuICBtZW1iZXJfaWQ6IHN0cmluZ1xuICBtZW1iZXJfbmFtZTogc3RyaW5nXG4gIGN5Y2xlX251bWJlcjogbnVtYmVyXG4gIGFtb3VudDogbnVtYmVyXG4gIGR1ZV9kYXRlOiBzdHJpbmdcbiAgcGFpZF9kYXRlOiBzdHJpbmcgfCBudWxsXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ3BhaWQnIHwgJ292ZXJkdWUnXG4gIHBheW1lbnRfbWV0aG9kOiBzdHJpbmcgfCBudWxsXG4gIG5vdGVzOiBzdHJpbmcgfCBudWxsXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBOb3RpZmljYXRpb24ge1xuICBpZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBncm91cF9pZDogc3RyaW5nIHwgbnVsbFxuICBncm91cF9uYW1lPzogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIHR5cGU6ICdwYXltZW50X2R1ZScgfCAncGF5bWVudF9yZWNlaXZlZCcgfCAnZGlzYnVyc2VtZW50JyB8ICdnZW5lcmFsJ1xuICBhbW91bnQ/OiBudW1iZXJcbiAgcmVhZDogYm9vbGVhblxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuLy8g2YXZgdin2KrZititINin2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuY29uc3QgU1RPUkFHRV9LRVlTID0ge1xuICBHUk9VUFM6ICdzYXJmYV9ncm91cHMnLFxuICBNRU1CRVJTOiAnc2FyZmFfbWVtYmVycycsXG4gIFBBWU1FTlRTOiAnc2FyZmFfcGF5bWVudHMnLFxuICBOT1RJRklDQVRJT05TOiAnc2FyZmFfbm90aWZpY2F0aW9ucycsXG4gIENVUlJFTlRfVVNFUjogJ3NhcmZhX2N1cnJlbnRfdXNlcidcbn1cblxuLy8g2K/ZiNin2YQg2YXYs9in2LnYr9ipINmE2YTYqtiu2LLZitmGINin2YTZhdit2YTZilxuZXhwb3J0IGNvbnN0IHN0b3JhZ2UgPSB7XG4gIC8vINin2YTZhdis2YXZiNi52KfYqlxuICBnZXRHcm91cHM6ICgpOiBHcm91cFtdID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBbXVxuICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWVMuR1JPVVBTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZUdyb3VwczogKGdyb3VwczogR3JvdXBbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkdST1VQUywgSlNPTi5zdHJpbmdpZnkoZ3JvdXBzKSlcbiAgfSxcblxuICBhZGRHcm91cDogKGdyb3VwOiBPbWl0PEdyb3VwLCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IEdyb3VwID0+IHtcbiAgICBjb25zdCBuZXdHcm91cDogR3JvdXAgPSB7XG4gICAgICAuLi5ncm91cCxcbiAgICAgIGlkOiBnZW5lcmF0ZUlkKCksXG4gICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gICAgXG4gICAgY29uc3QgZ3JvdXBzID0gc3RvcmFnZS5nZXRHcm91cHMoKVxuICAgIGdyb3Vwcy5wdXNoKG5ld0dyb3VwKVxuICAgIHN0b3JhZ2Uuc2F2ZUdyb3Vwcyhncm91cHMpXG4gICAgXG4gICAgcmV0dXJuIG5ld0dyb3VwXG4gIH0sXG5cbiAgdXBkYXRlR3JvdXA6IChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPEdyb3VwPik6IEdyb3VwIHwgbnVsbCA9PiB7XG4gICAgY29uc3QgZ3JvdXBzID0gc3RvcmFnZS5nZXRHcm91cHMoKVxuICAgIGNvbnN0IGluZGV4ID0gZ3JvdXBzLmZpbmRJbmRleChnID0+IGcuaWQgPT09IGlkKVxuICAgIFxuICAgIGlmIChpbmRleCA9PT0gLTEpIHJldHVybiBudWxsXG4gICAgXG4gICAgZ3JvdXBzW2luZGV4XSA9IHtcbiAgICAgIC4uLmdyb3Vwc1tpbmRleF0sXG4gICAgICAuLi51cGRhdGVzLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuICAgIFxuICAgIHN0b3JhZ2Uuc2F2ZUdyb3Vwcyhncm91cHMpXG4gICAgcmV0dXJuIGdyb3Vwc1tpbmRleF1cbiAgfSxcblxuICBnZXRHcm91cEJ5SWQ6IChpZDogc3RyaW5nKTogR3JvdXAgfCBudWxsID0+IHtcbiAgICBjb25zdCBncm91cHMgPSBzdG9yYWdlLmdldEdyb3VwcygpXG4gICAgcmV0dXJuIGdyb3Vwcy5maW5kKGcgPT4gZy5pZCA9PT0gaWQpIHx8IG51bGxcbiAgfSxcblxuICAvLyDYp9mE2KPYudi22KfYoVxuICBnZXRNZW1iZXJzOiAoKTogTWVtYmVyW10gPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIFtdXG4gICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFNUT1JBR0VfS0VZUy5NRU1CRVJTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZU1lbWJlcnM6IChtZW1iZXJzOiBNZW1iZXJbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLk1FTUJFUlMsIEpTT04uc3RyaW5naWZ5KG1lbWJlcnMpKVxuICB9LFxuXG4gIGdldE1lbWJlcnNCeUdyb3VwSWQ6IChncm91cElkOiBzdHJpbmcpOiBNZW1iZXJbXSA9PiB7XG4gICAgcmV0dXJuIHN0b3JhZ2UuZ2V0TWVtYmVycygpLmZpbHRlcihtID0+IG0uZ3JvdXBfaWQgPT09IGdyb3VwSWQpXG4gIH0sXG5cbiAgYWRkTWVtYmVyOiAobWVtYmVyOiBPbWl0PE1lbWJlciwgJ2lkJyB8ICdqb2luZWRfYXQnPik6IE1lbWJlciA9PiB7XG4gICAgY29uc3QgbmV3TWVtYmVyOiBNZW1iZXIgPSB7XG4gICAgICAuLi5tZW1iZXIsXG4gICAgICBpZDogZ2VuZXJhdGVJZCgpLFxuICAgICAgam9pbmVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gICAgXG4gICAgY29uc3QgbWVtYmVycyA9IHN0b3JhZ2UuZ2V0TWVtYmVycygpXG4gICAgbWVtYmVycy5wdXNoKG5ld01lbWJlcilcbiAgICBzdG9yYWdlLnNhdmVNZW1iZXJzKG1lbWJlcnMpXG4gICAgXG4gICAgcmV0dXJuIG5ld01lbWJlclxuICB9LFxuXG4gIC8vINin2YTZhdiv2YHZiNi52KfYqlxuICBnZXRQYXltZW50czogKCk6IFBheW1lbnRbXSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gW11cbiAgICBjb25zdCBkYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU1RPUkFHRV9LRVlTLlBBWU1FTlRTKVxuICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gIH0sXG5cbiAgc2F2ZVBheW1lbnRzOiAocGF5bWVudHM6IFBheW1lbnRbXSkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVlTLlBBWU1FTlRTLCBKU09OLnN0cmluZ2lmeShwYXltZW50cykpXG4gIH0sXG5cbiAgZ2V0UGF5bWVudHNCeUdyb3VwSWQ6IChncm91cElkOiBzdHJpbmcpOiBQYXltZW50W10gPT4ge1xuICAgIHJldHVybiBzdG9yYWdlLmdldFBheW1lbnRzKCkuZmlsdGVyKHAgPT4gcC5ncm91cF9pZCA9PT0gZ3JvdXBJZClcbiAgfSxcblxuICAvLyDYp9mE2KXYtNi52KfYsdin2KpcbiAgZ2V0Tm90aWZpY2F0aW9uczogKCk6IE5vdGlmaWNhdGlvbltdID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBbXVxuICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWVMuTk9USUZJQ0FUSU9OUylcbiAgICByZXR1cm4gZGF0YSA/IEpTT04ucGFyc2UoZGF0YSkgOiBbXVxuICB9LFxuXG4gIHNhdmVOb3RpZmljYXRpb25zOiAobm90aWZpY2F0aW9uczogTm90aWZpY2F0aW9uW10pID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFNUT1JBR0VfS0VZUy5OT1RJRklDQVRJT05TLCBKU09OLnN0cmluZ2lmeShub3RpZmljYXRpb25zKSlcbiAgfSxcblxuICBhZGROb3RpZmljYXRpb246IChub3RpZmljYXRpb246IE9taXQ8Tm90aWZpY2F0aW9uLCAnaWQnIHwgJ2NyZWF0ZWRfYXQnPik6IE5vdGlmaWNhdGlvbiA9PiB7XG4gICAgY29uc3QgbmV3Tm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb24gPSB7XG4gICAgICAuLi5ub3RpZmljYXRpb24sXG4gICAgICBpZDogZ2VuZXJhdGVJZCgpLFxuICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuICAgIFxuICAgIGNvbnN0IG5vdGlmaWNhdGlvbnMgPSBzdG9yYWdlLmdldE5vdGlmaWNhdGlvbnMoKVxuICAgIG5vdGlmaWNhdGlvbnMudW5zaGlmdChuZXdOb3RpZmljYXRpb24pIC8vINil2LbYp9mB2Kkg2YHZiiDYp9mE2YXZgtiv2YXYqVxuICAgIHN0b3JhZ2Uuc2F2ZU5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucylcbiAgICBcbiAgICByZXR1cm4gbmV3Tm90aWZpY2F0aW9uXG4gIH0sXG5cbiAgLy8g2KfZhNmF2LPYqtiu2K/ZhSDYp9mE2K3Yp9mE2YpcbiAgZ2V0Q3VycmVudFVzZXI6ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsXG4gICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFNUT1JBR0VfS0VZUy5DVVJSRU5UX1VTRVIpXG4gICAgcmV0dXJuIGRhdGEgPyBKU09OLnBhcnNlKGRhdGEpIDogbnVsbFxuICB9LFxuXG4gIHNldEN1cnJlbnRVc2VyOiAodXNlcjogYW55KSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTVE9SQUdFX0tFWVMuQ1VSUkVOVF9VU0VSLCBKU09OLnN0cmluZ2lmeSh1c2VyKSlcbiAgfSxcblxuICAvLyDZhdiz2K0g2KzZhdmK2Lkg2KfZhNio2YrYp9mG2KfYqlxuICBjbGVhckFsbDogKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgT2JqZWN0LnZhbHVlcyhTVE9SQUdFX0tFWVMpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSlcbiAgICB9KVxuICB9XG59XG5cbi8vINiv2KfZhNipINiq2YjZhNmK2K8g2YXYudix2YEg2YHYsdmK2K9cbmZ1bmN0aW9uIGdlbmVyYXRlSWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoMzYpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIpXG59XG5cbi8vINiq2YfZitim2Kkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KrYrNix2YrYqNmK2KlcbmV4cG9ydCBjb25zdCBpbml0aWFsaXplTW9ja0RhdGEgPSAoKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gIFxuICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDYqNmK2KfZhtin2Kog2YXYs9io2YLYqVxuICBjb25zdCBleGlzdGluZ0dyb3VwcyA9IHN0b3JhZ2UuZ2V0R3JvdXBzKClcbiAgaWYgKGV4aXN0aW5nR3JvdXBzLmxlbmd0aCA+IDApIHJldHVyblxuICBcbiAgLy8g2KXZhti02KfYoSDZhdiz2KrYrtiv2YUg2KrYrNix2YrYqNmKXG4gIGNvbnN0IG1vY2tVc2VyID0ge1xuICAgIGlkOiAndXNlcl8xJyxcbiAgICBlbWFpbDogJ3VzZXJAZXhhbXBsZS5jb20nLFxuICAgIGZ1bGxfbmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivJyxcbiAgICBhdmF0YXJfdXJsOiBudWxsXG4gIH1cbiAgc3RvcmFnZS5zZXRDdXJyZW50VXNlcihtb2NrVXNlcilcbiAgXG4gIC8vINil2YbYtNin2KEg2YXYrNmF2YjYudin2Kog2KrYrNix2YrYqNmK2KlcbiAgY29uc3QgbW9ja0dyb3VwczogR3JvdXBbXSA9IFtcbiAgICB7XG4gICAgICBpZDogJ2dyb3VwXzEnLFxuICAgICAgbmFtZTogJ9i12YbYr9mI2YIg2KfZhNij2LXYr9mC2KfYoScsXG4gICAgICBkZXNjcmlwdGlvbjogJ9i12YbYr9mI2YIg2KrYudin2YjZhtmKINmE2YTYo9i12K/Zgtin2KEg2KfZhNmF2YLYsdio2YrZhicsXG4gICAgICBhZG1pbl9pZDogJ3VzZXJfMScsXG4gICAgICBtZW1iZXJfY291bnQ6IDEwLFxuICAgICAgY29udHJpYnV0aW9uX2Ftb3VudDogMTAwMCxcbiAgICAgIGN5Y2xlX2R1cmF0aW9uX21vbnRoczogMTAsXG4gICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgc3RhcnRfZGF0ZTogJzIwMjQtMDEtMDEnLFxuICAgICAgZW5kX2RhdGU6ICcyMDI0LTEwLTMxJyxcbiAgICAgIGRyYXdfY29tcGxldGVkOiB0cnVlLFxuICAgICAgY3VycmVudF9jeWNsZTogMixcbiAgICAgIGNyZWF0ZWRfYXQ6ICcyMDIzLTEyLTE1VDAwOjAwOjAwWicsXG4gICAgICB1cGRhdGVkX2F0OiAnMjAyMy0xMi0xNVQwMDowMDowMFonXG4gICAgfVxuICBdXG4gIFxuICBzdG9yYWdlLnNhdmVHcm91cHMobW9ja0dyb3Vwcylcbn1cbiJdLCJuYW1lcyI6WyJTVE9SQUdFX0tFWVMiLCJHUk9VUFMiLCJNRU1CRVJTIiwiUEFZTUVOVFMiLCJOT1RJRklDQVRJT05TIiwiQ1VSUkVOVF9VU0VSIiwic3RvcmFnZSIsImdldEdyb3VwcyIsImRhdGEiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwic2F2ZUdyb3VwcyIsImdyb3VwcyIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJhZGRHcm91cCIsImdyb3VwIiwibmV3R3JvdXAiLCJpZCIsImdlbmVyYXRlSWQiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlZF9hdCIsInB1c2giLCJ1cGRhdGVHcm91cCIsInVwZGF0ZXMiLCJpbmRleCIsImZpbmRJbmRleCIsImciLCJnZXRHcm91cEJ5SWQiLCJmaW5kIiwiZ2V0TWVtYmVycyIsInNhdmVNZW1iZXJzIiwibWVtYmVycyIsImdldE1lbWJlcnNCeUdyb3VwSWQiLCJncm91cElkIiwiZmlsdGVyIiwibSIsImdyb3VwX2lkIiwiYWRkTWVtYmVyIiwibWVtYmVyIiwibmV3TWVtYmVyIiwiam9pbmVkX2F0IiwiZ2V0UGF5bWVudHMiLCJzYXZlUGF5bWVudHMiLCJwYXltZW50cyIsImdldFBheW1lbnRzQnlHcm91cElkIiwicCIsImdldE5vdGlmaWNhdGlvbnMiLCJzYXZlTm90aWZpY2F0aW9ucyIsIm5vdGlmaWNhdGlvbnMiLCJhZGROb3RpZmljYXRpb24iLCJub3RpZmljYXRpb24iLCJuZXdOb3RpZmljYXRpb24iLCJ1bnNoaWZ0IiwiZ2V0Q3VycmVudFVzZXIiLCJzZXRDdXJyZW50VXNlciIsInVzZXIiLCJjbGVhckFsbCIsIk9iamVjdCIsInZhbHVlcyIsImZvckVhY2giLCJrZXkiLCJyZW1vdmVJdGVtIiwibm93IiwidG9TdHJpbmciLCJNYXRoIiwicmFuZG9tIiwic3Vic3RyIiwiaW5pdGlhbGl6ZU1vY2tEYXRhIiwiZXhpc3RpbmdHcm91cHMiLCJsZW5ndGgiLCJtb2NrVXNlciIsImVtYWlsIiwiZnVsbF9uYW1lIiwiYXZhdGFyX3VybCIsIm1vY2tHcm91cHMiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJhZG1pbl9pZCIsIm1lbWJlcl9jb3VudCIsImNvbnRyaWJ1dGlvbl9hbW91bnQiLCJjeWNsZV9kdXJhdGlvbl9tb250aHMiLCJzdGF0dXMiLCJzdGFydF9kYXRlIiwiZW5kX2RhdGUiLCJkcmF3X2NvbXBsZXRlZCIsImN1cnJlbnRfY3ljbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/storage.ts\n"));

/***/ })

});