"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/page",{

/***/ "(app-pages-browser)/./src/app/groups/page.tsx":
/*!*********************************!*\
  !*** ./src/app/groups/page.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroups = [\n    {\n        id: \"1\",\n        name: \"صندوق الأصدقاء\",\n        description: \"صندوق تعاوني للأصدقاء المقربين\",\n        admin_id: \"1\",\n        member_count: 10,\n        contribution_amount: 1000,\n        cycle_duration_months: 10,\n        status: \"active\",\n        start_date: \"2024-01-01\",\n        end_date: \"2024-10-31\",\n        current_cycle: 2,\n        my_position: 3,\n        next_disbursement: \"2024-03-01\",\n        total_collected: 20000,\n        created_at: \"2023-12-15\"\n    },\n    {\n        id: \"2\",\n        name: \"صندوق العائلة\",\n        description: \"صندوق تعاوني لأفراد العائلة\",\n        admin_id: \"2\",\n        member_count: 8,\n        contribution_amount: 2000,\n        cycle_duration_months: 8,\n        status: \"draft\",\n        start_date: null,\n        end_date: null,\n        current_cycle: 0,\n        my_position: null,\n        next_disbursement: null,\n        total_collected: 0,\n        created_at: \"2024-02-01\"\n    },\n    {\n        id: \"3\",\n        name: \"صندوق العمل\",\n        description: \"صندوق تعاوني لزملاء العمل\",\n        admin_id: \"1\",\n        member_count: 12,\n        contribution_amount: 1500,\n        cycle_duration_months: 12,\n        status: \"completed\",\n        start_date: \"2023-01-01\",\n        end_date: \"2023-12-31\",\n        current_cycle: 12,\n        my_position: 5,\n        next_disbursement: null,\n        total_collected: 216000,\n        created_at: \"2022-12-01\"\n    }\n];\nfunction GroupsPage() {\n    _s();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تهيئة البيانات التجريبية\n        (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.initializeMockData)();\n        // تحميل البيانات\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_9__.storage.getCurrentUser();\n        const allGroups = _lib_storage__WEBPACK_IMPORTED_MODULE_9__.storage.getGroups();\n        setUser(currentUser);\n        setGroups(allGroups);\n        setLoading(false);\n    }, []);\n    const filteredGroups = groups.filter((group)=>{\n        var _group_description;\n        const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_group_description = group.description) === null || _group_description === void 0 ? void 0 : _group_description.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesStatus = statusFilter === \"all\" || group.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusVariant = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"success\";\n            case \"draft\":\n                return \"warning\";\n            case \"completed\":\n                return \"info\";\n            case \"cancelled\":\n                return \"danger\";\n            default:\n                return \"default\";\n        }\n    };\n    const isAdmin = (group)=>group.admin_id === (user === null || user === void 0 ? void 0 : user.id);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    user: user\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600 dark:text-gray-400\",\n                                children: \"جاري التحميل...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"مجموعاتي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                        children: \"إدارة الصناديق التعاونية التي تشارك فيها\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/groups/create\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إنشاء مجموعة جديدة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المجموعات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pr-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"مسودة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"نشط\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتمل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    filteredGroups.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-lg transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"pb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                            className: \"text-lg mb-2\",\n                                                            children: group.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                            className: \"text-sm\",\n                                                            children: group.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: getStatusVariant(group.status),\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.getStatusText)(group.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin(group) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 text-primary-600 mx-auto mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: \"الأعضاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                children: group.member_count\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-600 mx-auto mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: \"المساهمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(group.contribution_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    group.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"الدورة الحالية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            group.current_cycle,\n                                                                            \" من \",\n                                                                            group.cycle_duration_months\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            group.my_position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"ترتيبي:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: group.my_position\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            group.next_disbursement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"الصرف التالي:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-primary-600\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(group.next_disbursement)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    group.status === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"إجمالي المجمع:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(group.total_collected)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"تاريخ الإنشاء:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(group.created_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/groups/\".concat(group.id),\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"عرض التفاصيل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isAdmin(group) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, group.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this) : /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"لا توجد مجموعات تطابق البحث\" : \"لا توجد مجموعات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلتر\" : \"ابدأ بإنشاء مجموعة جديدة للصندوق التعاوني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                !searchTerm && statusFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/groups/create\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"إنشاء مجموعة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupsPage, \"LsmMqzOniITWxyWz3z2SEPvzKMA=\");\n_c = GroupsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/page.tsx\n"));

/***/ })

});