"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./src/app/groups/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/groups/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroup = {\n    id: \"1\",\n    name: \"صندوق الأصدقاء\",\n    description: \"صندوق تعاوني للأصدقاء المقربين\",\n    admin_id: \"1\",\n    member_count: 10,\n    contribution_amount: 1000,\n    cycle_duration_months: 10,\n    status: \"active\",\n    start_date: \"2024-01-01\",\n    end_date: \"2024-10-31\",\n    draw_completed: true,\n    current_cycle: 2,\n    created_at: \"2023-12-15\"\n};\nconst mockMembers = [\n    {\n        id: \"1\",\n        user_id: \"1\",\n        full_name: \"أحمد محمد\",\n        position_in_draw: 3,\n        is_admin: true,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        user_id: \"2\",\n        full_name: \"محمد علي\",\n        position_in_draw: 1,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        user_id: \"3\",\n        full_name: \"سارة أحمد\",\n        position_in_draw: 2,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"4\",\n        user_id: \"4\",\n        full_name: \"فاطمة محمد\",\n        position_in_draw: 4,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"5\",\n        user_id: \"5\",\n        full_name: \"عبدالله سالم\",\n        position_in_draw: 5,\n        is_admin: false,\n        status: \"active\"\n    }\n];\nconst mockPayments = [\n    {\n        id: \"1\",\n        member_name: \"أحمد محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-01\"\n    },\n    {\n        id: \"2\",\n        member_name: \"محمد علي\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-02\"\n    },\n    {\n        id: \"3\",\n        member_name: \"سارة أحمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"pending\",\n        due_date: \"2024-02-15\"\n    },\n    {\n        id: \"4\",\n        member_name: \"فاطمة محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"overdue\",\n        due_date: \"2024-02-10\"\n    }\n];\nfunction GroupDetailsPage(param) {\n    let { params } = param;\n    _s();\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showLotteryModal, setShowLotteryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAdmin = (group === null || group === void 0 ? void 0 : group.admin_id) === (user === null || user === void 0 ? void 0 : user.id);\n    const totalCollected = payments.filter((p)=>p.status === \"paid\").length * group.contribution_amount;\n    const pendingPayments = payments.filter((p)=>p.status === \"pending\").length;\n    const overduePayments = payments.filter((p)=>p.status === \"overdue\").length;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getCurrentUser();\n        const groupData = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getGroupById(params.id);\n        const groupMembers = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembersByGroupId(params.id);\n        const groupPayments = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getPaymentsByGroupId(params.id);\n        setUser(currentUser);\n        setGroup(groupData);\n        setMembers(groupMembers);\n        setPayments(groupPayments);\n        setLoading(false);\n    }, [\n        params.id\n    ]);\n    const handleDrawLottery = async ()=>{\n        setShowLotteryModal(true);\n    };\n    const handleLotteryComplete = (results)=>{\n        try {\n            // تحديث مواضع الأعضاء\n            const updatedMembers = members.map((member)=>{\n                const result = results.find((r)=>r.member_id === member.id);\n                return result ? {\n                    ...member,\n                    position_in_draw: result.position\n                } : member;\n            });\n            // تحديث المجموعة لتكون القرعة مكتملة\n            const updatedGroup = {\n                ...group,\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            };\n            // حفظ التحديثات\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.saveMembers(_lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembers().map((m)=>updatedMembers.find((um)=>um.id === m.id) || m));\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.updateGroup(group.id, {\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            });\n            // تحديث الحالة المحلية\n            setMembers(updatedMembers);\n            setGroup(updatedGroup);\n            // إضافة إشعار\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.addNotification({\n                user_id: user.id,\n                group_id: group.id,\n                group_name: group.name,\n                title: \"تم إجراء القرعة\",\n                message: 'تم إجراء قرعة مجموعة \"'.concat(group.name, '\" وتحديد ترتيب الأعضاء'),\n                type: \"general\",\n                read: false\n            });\n            setShowLotteryModal(false);\n        } catch (error) {\n            console.error(\"Error completing lottery:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إعدادات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"الأعضاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: group.member_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المبلغ المجمع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(totalCollected)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: overduePayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 space-x-reverse\",\n                                children: [\n                                    {\n                                        id: \"overview\",\n                                        name: \"نظرة عامة\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                    },\n                                    {\n                                        id: \"members\",\n                                        name: \"الأعضاء\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    },\n                                    {\n                                        id: \"payments\",\n                                        name: \"المدفوعات\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات المجموعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"المساهمة الشهرية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدة الدورة:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.cycle_duration_months,\n                                                            \" شهر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ البداية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.start_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ النهاية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.end_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"الدورة الحالية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.current_cycle,\n                                                            \" من \",\n                                                            group.cycle_duration_months\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"القرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"ترتيب الصرف للأعضاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: group.draw_completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: members.sort((a, b)=>(a.position_in_draw || 0) - (b.position_in_draw || 0)).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-50 border border-primary-200\" : \"bg-gray-50 dark:bg-gray-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-600 text-white\" : \"bg-gray-200 text-gray-700\"),\n                                                                    children: member.position_in_draw\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-3 font-medium\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"info\",\n                                                                    className: \"mr-2\",\n                                                                    children: \"مسؤول\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        member.position_in_draw < group.current_cycle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, member.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"لم يتم إجراء القرعة بعد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onClick: handleDrawLottery,\n                                                    loading: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"إجراء القرعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"members\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"أعضاء المجموعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"قائمة بجميع أعضاء الصندوق التعاوني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 font-medium\",\n                                                                children: member.full_name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: member.position_in_draw ? \"الترتيب: \".concat(member.position_in_draw) : \"لم يتم تحديد الترتيب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: member.status === \"active\" ? \"success\" : \"default\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(member.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"payments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"مدفوعات الدورة الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: [\n                                            \"حالة المدفوعات للدورة رقم \",\n                                            group.current_cycle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: payments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: payment.member_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: payment.status === \"paid\" && payment.paid_date ? \"تم الدفع في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.paid_date)) : payment.status === \"pending\" ? \"مستحق في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date)) : \"متأخر منذ \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(payment.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: payment.status === \"paid\" ? \"success\" : payment.status === \"pending\" ? \"warning\" : \"danger\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(payment.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin && payment.status !== \"paid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"تأكيد الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, payment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetailsPage, \"1HTPcEVpDd+PhyViXfW7dgFeX5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GroupDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/[id]/page.tsx\n"));

/***/ })

});