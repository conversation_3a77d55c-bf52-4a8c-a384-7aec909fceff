import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatShortDate(date: string | Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date))
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'active':
    case 'paid':
    case 'completed':
      return 'text-green-600 bg-green-100'
    case 'pending':
    case 'scheduled':
      return 'text-yellow-600 bg-yellow-100'
    case 'overdue':
    case 'cancelled':
      return 'text-red-600 bg-red-100'
    case 'draft':
      return 'text-gray-600 bg-gray-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function getStatusText(status: string): string {
  switch (status) {
    case 'active':
      return 'نشط'
    case 'inactive':
      return 'غير نشط'
    case 'draft':
      return 'مسودة'
    case 'completed':
      return 'مكتمل'
    case 'cancelled':
      return 'ملغي'
    case 'pending':
      return 'في الانتظار'
    case 'paid':
      return 'مدفوع'
    case 'overdue':
      return 'متأخر'
    case 'scheduled':
      return 'مجدول'
    default:
      return status
  }
}
