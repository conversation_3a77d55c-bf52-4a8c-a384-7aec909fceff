const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل تطبيق صرفة...');
console.log('📁 مجلد العمل:', __dirname);

// تشغيل Next.js
const nextBin = path.join(__dirname, 'node_modules', '.bin', 'next.cmd');
const child = spawn(nextBin, ['dev'], {
  stdio: 'inherit',
  cwd: __dirname,
  shell: true
});

child.on('error', (error) => {
  console.error('❌ خطأ في تشغيل التطبيق:', error.message);
});

child.on('close', (code) => {
  console.log(`✅ تم إغلاق التطبيق برمز: ${code}`);
});

console.log('📱 التطبيق سيعمل على: http://localhost:3000');
console.log('⏳ انتظر قليلاً حتى يكتمل التحميل...');
