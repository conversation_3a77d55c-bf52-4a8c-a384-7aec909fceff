export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      groups: {
        Row: {
          id: string
          name: string
          description: string | null
          admin_id: string
          member_count: number
          contribution_amount: number
          cycle_duration_months: number
          status: 'draft' | 'active' | 'completed' | 'cancelled'
          start_date: string | null
          end_date: string | null
          draw_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          admin_id: string
          member_count: number
          contribution_amount: number
          cycle_duration_months: number
          status?: 'draft' | 'active' | 'completed' | 'cancelled'
          start_date?: string | null
          end_date?: string | null
          draw_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          admin_id?: string
          member_count?: number
          contribution_amount?: number
          cycle_duration_months?: number
          status?: 'draft' | 'active' | 'completed' | 'cancelled'
          start_date?: string | null
          end_date?: string | null
          draw_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          position_in_draw: number | null
          is_admin: boolean
          joined_at: string
          status: 'active' | 'inactive' | 'removed'
        }
        Insert: {
          id?: string
          group_id: string
          user_id: string
          position_in_draw?: number | null
          is_admin?: boolean
          joined_at?: string
          status?: 'active' | 'inactive' | 'removed'
        }
        Update: {
          id?: string
          group_id?: string
          user_id?: string
          position_in_draw?: number | null
          is_admin?: boolean
          joined_at?: string
          status?: 'active' | 'inactive' | 'removed'
        }
      }
      payments: {
        Row: {
          id: string
          group_id: string
          member_id: string
          cycle_number: number
          amount: number
          due_date: string
          paid_date: string | null
          status: 'pending' | 'paid' | 'overdue'
          payment_method: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          group_id: string
          member_id: string
          cycle_number: number
          amount: number
          due_date: string
          paid_date?: string | null
          status?: 'pending' | 'paid' | 'overdue'
          payment_method?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          group_id?: string
          member_id?: string
          cycle_number?: number
          amount?: number
          due_date?: string
          paid_date?: string | null
          status?: 'pending' | 'paid' | 'overdue'
          payment_method?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      disbursements: {
        Row: {
          id: string
          group_id: string
          recipient_id: string
          cycle_number: number
          amount: number
          disbursement_date: string
          status: 'scheduled' | 'completed' | 'cancelled'
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          group_id: string
          recipient_id: string
          cycle_number: number
          amount: number
          disbursement_date: string
          status?: 'scheduled' | 'completed' | 'cancelled'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          group_id?: string
          recipient_id?: string
          cycle_number?: number
          amount?: number
          disbursement_date?: string
          status?: 'scheduled' | 'completed' | 'cancelled'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          group_id: string | null
          title: string
          message: string
          type: 'payment_due' | 'payment_received' | 'disbursement' | 'general'
          read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          group_id?: string | null
          title: string
          message: string
          type: 'payment_due' | 'payment_received' | 'disbursement' | 'general'
          read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          group_id?: string | null
          title?: string
          message?: string
          type?: 'payment_due' | 'payment_received' | 'disbursement' | 'general'
          read?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
