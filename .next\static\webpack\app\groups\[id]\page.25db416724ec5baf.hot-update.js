"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./src/app/groups/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/groups/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* harmony import */ var _components_lottery_LotteryDrawModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/lottery/LotteryDrawModal */ \"(app-pages-browser)/./src/components/lottery/LotteryDrawModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroup = {\n    id: \"1\",\n    name: \"صندوق الأصدقاء\",\n    description: \"صندوق تعاوني للأصدقاء المقربين\",\n    admin_id: \"1\",\n    member_count: 10,\n    contribution_amount: 1000,\n    cycle_duration_months: 10,\n    status: \"active\",\n    start_date: \"2024-01-01\",\n    end_date: \"2024-10-31\",\n    draw_completed: true,\n    current_cycle: 2,\n    created_at: \"2023-12-15\"\n};\nconst mockMembers = [\n    {\n        id: \"1\",\n        user_id: \"1\",\n        full_name: \"أحمد محمد\",\n        position_in_draw: 3,\n        is_admin: true,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        user_id: \"2\",\n        full_name: \"محمد علي\",\n        position_in_draw: 1,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        user_id: \"3\",\n        full_name: \"سارة أحمد\",\n        position_in_draw: 2,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"4\",\n        user_id: \"4\",\n        full_name: \"فاطمة محمد\",\n        position_in_draw: 4,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"5\",\n        user_id: \"5\",\n        full_name: \"عبدالله سالم\",\n        position_in_draw: 5,\n        is_admin: false,\n        status: \"active\"\n    }\n];\nconst mockPayments = [\n    {\n        id: \"1\",\n        member_name: \"أحمد محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-01\"\n    },\n    {\n        id: \"2\",\n        member_name: \"محمد علي\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-02\"\n    },\n    {\n        id: \"3\",\n        member_name: \"سارة أحمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"pending\",\n        due_date: \"2024-02-15\"\n    },\n    {\n        id: \"4\",\n        member_name: \"فاطمة محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"overdue\",\n        due_date: \"2024-02-10\"\n    }\n];\nfunction GroupDetailsPage(param) {\n    let { params } = param;\n    _s();\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showLotteryModal, setShowLotteryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAdmin = (group === null || group === void 0 ? void 0 : group.admin_id) === (user === null || user === void 0 ? void 0 : user.id);\n    const totalCollected = payments.filter((p)=>p.status === \"paid\").length * group.contribution_amount;\n    const pendingPayments = payments.filter((p)=>p.status === \"pending\").length;\n    const overduePayments = payments.filter((p)=>p.status === \"overdue\").length;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getCurrentUser();\n        const groupData = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getGroupById(params.id);\n        const groupMembers = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembersByGroupId(params.id);\n        const groupPayments = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getPaymentsByGroupId(params.id);\n        setUser(currentUser);\n        setGroup(groupData);\n        setMembers(groupMembers);\n        setPayments(groupPayments);\n        setLoading(false);\n    }, [\n        params.id\n    ]);\n    const handleDrawLottery = async ()=>{\n        setShowLotteryModal(true);\n    };\n    const handleLotteryComplete = (results)=>{\n        try {\n            // تحديث مواضع الأعضاء\n            const updatedMembers = members.map((member)=>{\n                const result = results.find((r)=>r.member_id === member.id);\n                return result ? {\n                    ...member,\n                    position_in_draw: result.position\n                } : member;\n            });\n            // تحديث المجموعة لتكون القرعة مكتملة\n            const updatedGroup = {\n                ...group,\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            };\n            // حفظ التحديثات\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.saveMembers(_lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembers().map((m)=>updatedMembers.find((um)=>um.id === m.id) || m));\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.updateGroup(group.id, {\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            });\n            // تحديث الحالة المحلية\n            setMembers(updatedMembers);\n            setGroup(updatedGroup);\n            // إضافة إشعار\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.addNotification({\n                user_id: user.id,\n                group_id: group.id,\n                group_name: group.name,\n                title: \"تم إجراء القرعة\",\n                message: 'تم إجراء قرعة مجموعة \"'.concat(group.name, '\" وتحديد ترتيب الأعضاء'),\n                type: \"general\",\n                read: false\n            });\n            setShowLotteryModal(false);\n        } catch (error) {\n            console.error(\"Error completing lottery:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إعدادات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"الأعضاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: group.member_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المبلغ المجمع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(totalCollected)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: overduePayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 space-x-reverse\",\n                                children: [\n                                    {\n                                        id: \"overview\",\n                                        name: \"نظرة عامة\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                                    },\n                                    {\n                                        id: \"members\",\n                                        name: \"الأعضاء\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                    },\n                                    {\n                                        id: \"payments\",\n                                        name: \"المدفوعات\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات المجموعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"المساهمة الشهرية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدة الدورة:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.cycle_duration_months,\n                                                            \" شهر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ البداية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.start_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ النهاية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.end_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"الدورة الحالية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.current_cycle,\n                                                            \" من \",\n                                                            group.cycle_duration_months\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"القرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"ترتيب الصرف للأعضاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: group.draw_completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: members.sort((a, b)=>(a.position_in_draw || 0) - (b.position_in_draw || 0)).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-50 border border-primary-200\" : \"bg-gray-50 dark:bg-gray-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-600 text-white\" : \"bg-gray-200 text-gray-700\"),\n                                                                    children: member.position_in_draw\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-3 font-medium\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"info\",\n                                                                    className: \"mr-2\",\n                                                                    children: \"مسؤول\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        member.position_in_draw < group.current_cycle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, member.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"لم يتم إجراء القرعة بعد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onClick: handleDrawLottery,\n                                                    loading: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"إجراء القرعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"members\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"أعضاء المجموعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"قائمة بجميع أعضاء الصندوق التعاوني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 font-medium\",\n                                                                children: member.full_name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: member.position_in_draw ? \"الترتيب: \".concat(member.position_in_draw) : \"لم يتم تحديد الترتيب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: member.status === \"active\" ? \"success\" : \"default\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(member.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"payments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"مدفوعات الدورة الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: [\n                                            \"حالة المدفوعات للدورة رقم \",\n                                            group.current_cycle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: payments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: payment.member_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: payment.status === \"paid\" && payment.paid_date ? \"تم الدفع في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.paid_date)) : payment.status === \"pending\" ? \"مستحق في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date)) : \"متأخر منذ \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(payment.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: payment.status === \"paid\" ? \"success\" : payment.status === \"pending\" ? \"warning\" : \"danger\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(payment.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin && payment.status !== \"paid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"تأكيد الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, payment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            showLotteryModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lottery_LotteryDrawModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showLotteryModal,\n                onClose: ()=>setShowLotteryModal(false),\n                members: members,\n                onLotteryComplete: handleLotteryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 464,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetailsPage, \"1HTPcEVpDd+PhyViXfW7dgFeX5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GroupDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/lottery/LotteryDrawModal.tsx":
/*!*****************************************************!*\
  !*** ./src/components/lottery/LotteryDrawModal.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LotteryDrawModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shuffle,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shuffle,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shuffle,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shuffle,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_lottery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/lottery */ \"(app-pages-browser)/./src/lib/lottery.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LotteryDrawModal(param) {\n    let { isOpen, onClose, members, onLotteryComplete } = param;\n    _s();\n    const [drawType, setDrawType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"random\");\n    const [manualPositions, setManualPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    if (!isOpen) return null;\n    const handleRandomDraw = ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            // محاكاة تأخير للتأثير البصري\n            setTimeout(()=>{\n                const lotteryResults = (0,_lib_lottery__WEBPACK_IMPORTED_MODULE_3__.conductRandomLottery)(members);\n                setResults(lotteryResults);\n                setLoading(false);\n            }, 1500);\n        } catch (err) {\n            setError(\"حدث خطأ أثناء إجراء القرعة\");\n            setLoading(false);\n        }\n    };\n    const handleManualDraw = ()=>{\n        setError(\"\");\n        try {\n            const memberPositions = Object.entries(manualPositions).map((param)=>{\n                let [memberId, position] = param;\n                return {\n                    member_id: memberId,\n                    position\n                };\n            });\n            if (memberPositions.length !== members.length) {\n                setError(\"يجب تحديد ترتيب لجميع الأعضاء\");\n                return;\n            }\n            const lotteryResults = (0,_lib_lottery__WEBPACK_IMPORTED_MODULE_3__.conductManualLottery)(memberPositions);\n            setResults(lotteryResults);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"حدث خطأ أثناء إجراء القرعة\");\n        }\n    };\n    const handleConfirm = ()=>{\n        if (results) {\n            onLotteryComplete(results);\n            onClose();\n        }\n    };\n    const handleManualPositionChange = (memberId, position)=>{\n        const pos = parseInt(position);\n        if (isNaN(pos) || pos < 1 || pos > members.length) {\n            const newPositions = {\n                ...manualPositions\n            };\n            delete newPositions[memberId];\n            setManualPositions(newPositions);\n        } else {\n            setManualPositions((prev)=>({\n                    ...prev,\n                    [memberId]: pos\n                }));\n        }\n    };\n    const getMemberByResult = (result)=>{\n        return members.find((m)=>m.id === result.member_id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-900 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"إجراء القرعة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: !results ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                                        children: \"اختر نوع القرعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setDrawType(\"random\"),\n                                                className: \"p-4 border-2 rounded-lg text-center transition-colors \".concat(drawType === \"random\" ? \"border-primary-500 bg-primary-50 dark:bg-primary-900/20\" : \"border-gray-200 dark:border-gray-700 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-8 h-8 mx-auto mb-2 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: \"قرعة عشوائية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                        children: \"ترتيب عشوائي تماماً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setDrawType(\"manual\"),\n                                                className: \"p-4 border-2 rounded-lg text-center transition-colors \".concat(drawType === \"manual\" ? \"border-primary-500 bg-primary-50 dark:bg-primary-900/20\" : \"border-gray-200 dark:border-gray-700 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-8 h-8 mx-auto mb-2 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: \"ترتيب يدوي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                        children: \"تحديد الترتيب بنفسك\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this),\n                            drawType === \"random\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-16 h-16 mx-auto mb-4 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                                children: \"قرعة عشوائية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: \"سيتم ترتيب الأعضاء عشوائياً لتحديد دور كل عضو في الصرف\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        onClick: handleRandomDraw,\n                                        loading: loading,\n                                        size: \"lg\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 21\n                                            }, this),\n                                            loading ? \"جاري إجراء القرعة...\" : \"إجراء القرعة العشوائية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 17\n                            }, this),\n                            drawType === \"manual\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                                        children: \"حدد ترتيب الأعضاء\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-6\",\n                                        children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: member.full_name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 ml-2\",\n                                                                children: \"الترتيب:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"1\",\n                                                                max: members.length,\n                                                                value: manualPositions[member.id] || \"\",\n                                                                onChange: (e)=>handleManualPositionChange(member.id, e.target.value),\n                                                                className: \"w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-center text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, member.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        onClick: handleManualDraw,\n                                        size: \"lg\",\n                                        className: \"w-full\",\n                                        children: \"تأكيد الترتيب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 17\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shuffle_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-red-600 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-700 dark:text-red-300\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /* نتائج القرعة */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                                children: \"نتائج القرعة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-6\",\n                                children: results.sort((a, b)=>a.position - b.position).map((result)=>{\n                                    const member = getMemberByResult(result);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-medium text-sm\",\n                                                        children: result.position\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 font-medium text-gray-900 dark:text-white\",\n                                                        children: member === null || member === void 0 ? void 0 : member.full_name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: [\n                                                    \"الشهر \",\n                                                    result.position\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, result.member_id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"outline\",\n                                        onClick: ()=>setResults(null),\n                                        className: \"flex-1\",\n                                        children: \"إعادة القرعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        onClick: handleConfirm,\n                                        className: \"flex-1\",\n                                        children: \"تأكيد النتائج\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\lottery\\\\LotteryDrawModal.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n_s(LotteryDrawModal, \"fJ7mfJVSCq02RAjq+x6BXLssiKE=\");\n_c = LotteryDrawModal;\nvar _c;\n$RefreshReg$(_c, \"LotteryDrawModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lottery/LotteryDrawModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/lottery.ts":
/*!****************************!*\
  !*** ./src/lib/lottery.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arePaymentsComplete: function() { return /* binding */ arePaymentsComplete; },\n/* harmony export */   calculateTotalAmount: function() { return /* binding */ calculateTotalAmount; },\n/* harmony export */   conductManualLottery: function() { return /* binding */ conductManualLottery; },\n/* harmony export */   conductRandomLottery: function() { return /* binding */ conductRandomLottery; },\n/* harmony export */   getCurrentCycle: function() { return /* binding */ getCurrentCycle; },\n/* harmony export */   getCurrentRecipient: function() { return /* binding */ getCurrentRecipient; },\n/* harmony export */   getNextDisbursementDate: function() { return /* binding */ getNextDisbursementDate; },\n/* harmony export */   getPaymentCompletionPercentage: function() { return /* binding */ getPaymentCompletionPercentage; },\n/* harmony export */   validateLotteryResults: function() { return /* binding */ validateLotteryResults; }\n/* harmony export */ });\n// نظام القرعة للصندوق التعاوني\n/**\n * إجراء قرعة عشوائية لتحديد ترتيب الأعضاء\n */ function conductRandomLottery(members) {\n    // إنشاء نسخة من قائمة الأعضاء\n    const shuffledMembers = [\n        ...members\n    ];\n    // خلط القائمة عشوائياً باستخدام Fisher-Yates shuffle\n    for(let i = shuffledMembers.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffledMembers[i], shuffledMembers[j]] = [\n            shuffledMembers[j],\n            shuffledMembers[i]\n        ];\n    }\n    // إرجاع النتائج مع المواضع\n    return shuffledMembers.map((member, index)=>({\n            member_id: member.id,\n            position: index + 1\n        }));\n}\n/**\n * إجراء قرعة يدوية بناءً على ترتيب محدد\n */ function conductManualLottery(memberPositions) {\n    // التحقق من صحة البيانات\n    const positions = memberPositions.map((mp)=>mp.position);\n    const uniquePositions = new Set(positions);\n    if (positions.length !== uniquePositions.size) {\n        throw new Error(\"لا يمكن أن يكون لعضوين نفس الترتيب\");\n    }\n    const sortedPositions = [\n        ...positions\n    ].sort((a, b)=>a - b);\n    for(let i = 0; i < sortedPositions.length; i++){\n        if (sortedPositions[i] !== i + 1) {\n            throw new Error(\"يجب أن تكون المواضع متتالية بدءاً من 1\");\n        }\n    }\n    return memberPositions.sort((a, b)=>a.position - b.position);\n}\n/**\n * التحقق من صحة نتائج القرعة\n */ function validateLotteryResults(results, expectedMemberCount) {\n    // التحقق من عدد النتائج\n    if (results.length !== expectedMemberCount) {\n        return false;\n    }\n    // التحقق من تفرد معرفات الأعضاء\n    const memberIds = results.map((r)=>r.member_id);\n    const uniqueMemberIds = new Set(memberIds);\n    if (memberIds.length !== uniqueMemberIds.size) {\n        return false;\n    }\n    // التحقق من تتالي المواضع\n    const positions = results.map((r)=>r.position).sort((a, b)=>a - b);\n    for(let i = 0; i < positions.length; i++){\n        if (positions[i] !== i + 1) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * حساب الدورة الحالية بناءً على تاريخ البداية\n */ function getCurrentCycle(startDate, cycleDurationMonths) {\n    const start = new Date(startDate);\n    const now = new Date();\n    const monthsDiff = (now.getFullYear() - start.getFullYear()) * 12 + (now.getMonth() - start.getMonth());\n    return Math.min(Math.floor(monthsDiff) + 1, cycleDurationMonths);\n}\n/**\n * حساب تاريخ الصرف التالي\n */ function getNextDisbursementDate(startDate, currentCycle) {\n    const start = new Date(startDate);\n    const nextDate = new Date(start);\n    nextDate.setMonth(start.getMonth() + currentCycle);\n    return nextDate.toISOString().split(\"T\")[0];\n}\n/**\n * تحديد العضو المستحق للصرف في الدورة الحالية\n */ function getCurrentRecipient(members, currentCycle) {\n    return members.find((member)=>member.position_in_draw === currentCycle) || null;\n}\n/**\n * حساب إجمالي المبلغ المجمع في دورة معينة\n */ function calculateTotalAmount(memberCount, contributionAmount) {\n    return memberCount * contributionAmount;\n}\n/**\n * التحقق من اكتمال المدفوعات لدورة معينة\n */ function arePaymentsComplete(payments, memberCount) {\n    const paidPayments = payments.filter((payment)=>payment.status === \"paid\");\n    return paidPayments.length === memberCount;\n}\n/**\n * حساب النسبة المئوية للمدفوعات المكتملة\n */ function getPaymentCompletionPercentage(payments, memberCount) {\n    const paidPayments = payments.filter((payment)=>payment.status === \"paid\");\n    return Math.round(paidPayments.length / memberCount * 100);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/lottery.ts\n"));

/***/ })

});