/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/notifications/page";
exports.ids = ["app/notifications/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnotifications%2Fpage&page=%2Fnotifications%2Fpage&appPaths=%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnotifications%2Fpage&page=%2Fnotifications%2Fpage&appPaths=%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'notifications',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/notifications/page.tsx */ \"(rsc)/./src/app/notifications/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/notifications/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/notifications/page\",\n        pathname: \"/notifications\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnotifications%2Fpage&page=%2Fnotifications%2Fpage&appPaths=%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp%5Cnotifications%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp%5Cnotifications%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/notifications/page.tsx */ \"(ssr)/./src/app/notifications/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWRtaW5pc3RyYXRvciU1QyVEOCVCNSVEOCVCMSVEOSU4MSVEOCVBOSUyMCVEOCVCNSVEOSU4NiVEOCVBRiVEOSU4OCVEOSU4MiU1Q3NyYyU1Q2FwcCU1Q25vdGlmaWNhdGlvbnMlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zYXJmYS8/OGI2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxc2LXYsdmB2Kkg2LXZhtiv2YjZglxcXFxzcmNcXFxcYXBwXFxcXG5vdGlmaWNhdGlvbnNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp%5Cnotifications%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/notifications/page.tsx":
/*!****************************************!*\
  !*** ./src/app/notifications/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(ssr)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockNotifications = [\n    {\n        id: \"1\",\n        title: \"مساهمة مستحقة\",\n        message: \"مساهمتك الشهرية لصندوق الأصدقاء مستحقة اليوم\",\n        type: \"payment_due\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-15T10:00:00Z\"\n    },\n    {\n        id: \"2\",\n        title: \"تم استلام مساهمة\",\n        message: \"تم استلام مساهمة محمد علي لصندوق الأصدقاء\",\n        type: \"payment_received\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-14T15:30:00Z\"\n    },\n    {\n        id: \"3\",\n        title: \"موعد الصرف\",\n        message: \"سيتم صرف مبلغ 10,000 ريال لسارة أحمد غداً\",\n        type: \"disbursement\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 10000,\n        read: true,\n        created_at: \"2024-02-13T09:00:00Z\"\n    },\n    {\n        id: \"4\",\n        title: \"عضو جديد\",\n        message: \"انضم عبدالله سالم إلى صندوق العائلة\",\n        type: \"general\",\n        group_name: \"صندوق العائلة\",\n        read: true,\n        created_at: \"2024-02-12T14:20:00Z\"\n    },\n    {\n        id: \"5\",\n        title: \"تم إجراء القرعة\",\n        message: \"تم إجراء قرعة صندوق الأصدقاء وتحديد ترتيب الأعضاء\",\n        type: \"general\",\n        group_name: \"صندوق الأصدقاء\",\n        read: true,\n        created_at: \"2024-01-15T11:00:00Z\"\n    }\n];\nfunction NotificationsPage() {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockNotifications);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const filteredNotifications = notifications.filter((notification)=>filter === \"all\" || notification.type === filter);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const markAsRead = async (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Implement actual API call\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            setNotifications((prev)=>prev.map((notification)=>({\n                        ...notification,\n                        read: true\n                    })));\n        } catch (error) {\n            console.error(\"Error marking all as read:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteNotification = async (notificationId)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== notificationId));\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"payment_due\":\n            case \"payment_received\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            case \"disbursement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"general\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationColor = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"text-red-600 bg-red-100\";\n            case \"payment_received\":\n                return \"text-green-600 bg-green-100\";\n            case \"disbursement\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"general\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getTypeText = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"مساهمة مستحقة\";\n            case \"payment_received\":\n                return \"مساهمة مستلمة\";\n            case \"disbursement\":\n                return \"صرف\";\n            case \"general\":\n                return \"عام\";\n            default:\n                return type;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                        children: unreadCount > 0 ? `لديك ${unreadCount} إشعار غير مقروء` : \"جميع الإشعارات مقروءة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onClick: markAllAsRead,\n                                loading: loading,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديد الكل كمقروء\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تصفية الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        {\n                                            key: \"all\",\n                                            label: \"الكل\"\n                                        },\n                                        {\n                                            key: \"payment_due\",\n                                            label: \"مساهمات مستحقة\"\n                                        },\n                                        {\n                                            key: \"payment_received\",\n                                            label: \"مساهمات مستلمة\"\n                                        },\n                                        {\n                                            key: \"disbursement\",\n                                            label: \"صرف\"\n                                        },\n                                        {\n                                            key: \"general\",\n                                            label: \"عام\"\n                                        }\n                                    ].map((filterOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilter(filterOption.key),\n                                            className: `px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === filterOption.key ? \"bg-primary-600 text-white\" : \"bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700\"}`,\n                                            children: filterOption.label\n                                        }, filterOption.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: filteredNotifications.length > 0 ? filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: `transition-all hover:shadow-md ${!notification.read ? \"border-primary-200 bg-primary-50/30 dark:bg-primary-900/10\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4 space-x-reverse flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-2 rounded-full ${getNotificationColor(notification.type)}`,\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: `font-semibold ${!notification.read ? \"text-gray-900 dark:text-white\" : \"text-gray-700 dark:text-gray-300\"}`,\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: notification.group_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 256,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(notification.created_at)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 257,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            notification.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-primary-600\",\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(notification.amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"default\",\n                                                                        children: getTypeText(notification.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse mr-4\",\n                                                children: [\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsRead(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-primary-600 transition-colors\",\n                                                        title: \"تحديد كمقروء\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>deleteNotification(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                        title: \"حذف الإشعار\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: filter === \"all\" ? \"لا توجد إشعارات حالياً\" : `لا توجد إشعارات من نوع \"${getTypeText(filter)}\"`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/notifications/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Menu,Moon,Sun,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Navbar({ user }) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const toggleDarkMode = ()=>{\n        setIsDarkMode(!isDarkMode);\n        document.documentElement.classList.toggle(\"dark\");\n    };\n    const handleSignOut = async ()=>{\n        // TODO: Implement sign out logic\n        router.push(\"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-primary-600\",\n                                    children: \"صرفة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4 space-x-reverse\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"text-gray-700 dark:text-gray-300 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium\",\n                                        children: \"لوحة التحكم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/groups\",\n                                        className: \"text-gray-700 dark:text-gray-300 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium\",\n                                        children: \"المجموعات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/notifications\",\n                                        className: \"text-gray-700 dark:text-gray-300 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                            children: [\n                                                user.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: user.avatar_url,\n                                                    alt: user.full_name || user.email,\n                                                    className: \"w-8 h-8 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: user.full_name || user.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleDarkMode,\n                                        className: \"p-2 text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                        children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 63\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleSignOut,\n                                        className: \"text-gray-700 dark:text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"تسجيل الخروج\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleDarkMode,\n                                        className: \"p-2 text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                        children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 63\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            children: \"تسجيل الدخول\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: \"sm\",\n                                            children: \"إنشاء حساب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary-600 p-2\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Menu_Moon_Sun_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800\",\n                    children: [\n                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"لوحة التحكم\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/groups\",\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"المجموعات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/notifications\",\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"الإشعارات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSignOut,\n                                    className: \"block w-full text-right px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"تسجيل الخروج\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/login\",\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/register\",\n                                    className: \"block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                                    children: \"إنشاء حساب\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleDarkMode,\n                            className: \"block w-full text-right px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600\",\n                            children: isDarkMode ? \"الوضع النهاري\" : \"الوضع الليلي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Badge = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"default\", ...props }, ref)=>{\n    const variants = {\n        default: \"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n        success: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        warning: \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300\",\n        danger: \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        info: \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium\", variants[variant], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Badge.tsx\",\n        lineNumber: 19,\n        columnNumber: 7\n    }, undefined);\n});\nBadge.displayName = \"Badge\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"primary\", size = \"md\", loading, children, disabled, ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variants = {\n        primary: \"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500\",\n        secondary: \"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500\",\n        outline: \"border-2 border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500\",\n        ghost: \"text-primary-600 hover:bg-primary-50 focus:ring-primary-500\",\n        danger: \"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500\"\n    };\n    const sizes = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-base\",\n        lg: \"px-6 py-3 text-lg\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 41,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 29,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-gray-500 dark:text-gray-400\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatShortDate: () => (/* binding */ formatShortDate),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusText: () => (/* binding */ getStatusText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat(\"ar-SA\", {\n        style: \"currency\",\n        currency: \"SAR\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"ar-SA\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatShortDate(date) {\n    return new Intl.DateTimeFormat(\"ar-SA\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction getStatusColor(status) {\n    switch(status){\n        case \"active\":\n        case \"paid\":\n        case \"completed\":\n            return \"text-green-600 bg-green-100\";\n        case \"pending\":\n        case \"scheduled\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"overdue\":\n        case \"cancelled\":\n            return \"text-red-600 bg-red-100\";\n        case \"draft\":\n            return \"text-gray-600 bg-gray-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getStatusText(status) {\n    switch(status){\n        case \"active\":\n            return \"نشط\";\n        case \"inactive\":\n            return \"غير نشط\";\n        case \"draft\":\n            return \"مسودة\";\n        case \"completed\":\n            return \"مكتمل\";\n        case \"cancelled\":\n            return \"ملغي\";\n        case \"pending\":\n            return \"في الانتظار\";\n        case \"paid\":\n            return \"مدفوع\";\n        case \"overdue\":\n            return \"متأخر\";\n        case \"scheduled\":\n            return \"مجدول\";\n        default:\n            return status;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"725f62ed6777\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2FyZmEvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzQ3ZjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MjVmNjJlZDY3NzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"صرفة - إدارة الصندوق التعاوني\",\n    description: \"تطبيق لإدارة الصناديق التعاونية الدورية (صرفة الصندوق)\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-arabic antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDYkw7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zYXJmYS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ9i12LHZgdipIC0g2KXYr9in2LHYqSDYp9mE2LXZhtiv2YjZgiDYp9mE2KrYudin2YjZhtmKJyxcbiAgZGVzY3JpcHRpb246ICfYqti32KjZitmCINmE2KXYr9in2LHYqSDYp9mE2LXZhtin2K/ZitmCINin2YTYqti52KfZiNmG2YrYqSDYp9mE2K/ZiNix2YrYqSAo2LXYsdmB2Kkg2KfZhNi12YbYr9mI2YIpJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImFyXCIgZGlyPVwicnRsXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJmb250LWFyYWJpYyBhbnRpYWxpYXNlZFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/notifications/page.tsx":
/*!****************************************!*\
  !*** ./src/app/notifications/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\صرفة صندوق\src\app\notifications\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnotifications%2Fpage&page=%2Fnotifications%2Fpage&appPaths=%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5C%D8%B5%D8%B1%D9%81%D8%A9%20%D8%B5%D9%86%D8%AF%D9%88%D9%82&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();