"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/page",{

/***/ "(app-pages-browser)/./src/app/groups/page.tsx":
/*!*********************************!*\
  !*** ./src/app/groups/page.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Eye,Plus,Search,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroups = [\n    {\n        id: \"1\",\n        name: \"صندوق الأصدقاء\",\n        description: \"صندوق تعاوني للأصدقاء المقربين\",\n        admin_id: \"1\",\n        member_count: 10,\n        contribution_amount: 1000,\n        cycle_duration_months: 10,\n        status: \"active\",\n        start_date: \"2024-01-01\",\n        end_date: \"2024-10-31\",\n        current_cycle: 2,\n        my_position: 3,\n        next_disbursement: \"2024-03-01\",\n        total_collected: 20000,\n        created_at: \"2023-12-15\"\n    },\n    {\n        id: \"2\",\n        name: \"صندوق العائلة\",\n        description: \"صندوق تعاوني لأفراد العائلة\",\n        admin_id: \"2\",\n        member_count: 8,\n        contribution_amount: 2000,\n        cycle_duration_months: 8,\n        status: \"draft\",\n        start_date: null,\n        end_date: null,\n        current_cycle: 0,\n        my_position: null,\n        next_disbursement: null,\n        total_collected: 0,\n        created_at: \"2024-02-01\"\n    },\n    {\n        id: \"3\",\n        name: \"صندوق العمل\",\n        description: \"صندوق تعاوني لزملاء العمل\",\n        admin_id: \"1\",\n        member_count: 12,\n        contribution_amount: 1500,\n        cycle_duration_months: 12,\n        status: \"completed\",\n        start_date: \"2023-01-01\",\n        end_date: \"2023-12-31\",\n        current_cycle: 12,\n        my_position: 5,\n        next_disbursement: null,\n        total_collected: 216000,\n        created_at: \"2022-12-01\"\n    }\n];\nfunction GroupsPage() {\n    _s();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تهيئة البيانات التجريبية\n        (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.initializeMockData)();\n        // تحميل البيانات\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_9__.storage.getCurrentUser();\n        const allGroups = _lib_storage__WEBPACK_IMPORTED_MODULE_9__.storage.getGroups();\n        setUser(currentUser);\n        setGroups(allGroups);\n        setLoading(false);\n    }, []);\n    const filteredGroups = groups.filter((group)=>{\n        var _group_description;\n        const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_group_description = group.description) === null || _group_description === void 0 ? void 0 : _group_description.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesStatus = statusFilter === \"all\" || group.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const getStatusVariant = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"success\";\n            case \"draft\":\n                return \"warning\";\n            case \"completed\":\n                return \"info\";\n            case \"cancelled\":\n                return \"danger\";\n            default:\n                return \"default\";\n        }\n    };\n    const isAdmin = (group)=>group.admin_id === mockUser.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"مجموعاتي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                        children: \"إدارة الصناديق التعاونية التي تشارك فيها\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/groups/create\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إنشاء مجموعة جديدة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المجموعات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pr-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"مسودة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"نشط\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"مكتمل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"cancelled\",\n                                                    children: \"ملغي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    filteredGroups.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-lg transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"pb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                            className: \"text-lg mb-2\",\n                                                            children: group.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                            className: \"text-sm\",\n                                                            children: group.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: getStatusVariant(group.status),\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.getStatusText)(group.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin(group) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 text-primary-600 mx-auto mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: \"الأعضاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                children: group.member_count\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-600 mx-auto mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: \"المساهمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(group.contribution_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    group.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"الدورة الحالية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            group.current_cycle,\n                                                                            \" من \",\n                                                                            group.cycle_duration_months\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            group.my_position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"ترتيبي:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: group.my_position\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            group.next_disbursement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"الصرف التالي:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-primary-600\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(group.next_disbursement)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    group.status === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"إجمالي المجمع:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(group.total_collected)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"تاريخ الإنشاء:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(group.created_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/groups/\".concat(group.id),\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"عرض التفاصيل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isAdmin(group) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, group.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this) : /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"لا توجد مجموعات تطابق البحث\" : \"لا توجد مجموعات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلتر\" : \"ابدأ بإنشاء مجموعة جديدة للصندوق التعاوني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                !searchTerm && statusFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/groups/create\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Eye_Plus_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"إنشاء مجموعة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupsPage, \"LsmMqzOniITWxyWz3z2SEPvzKMA=\");\n_c = GroupsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeMockData: function() { return /* binding */ initializeMockData; },\n/* harmony export */   storage: function() { return /* binding */ storage; }\n/* harmony export */ });\n// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    GROUPS: \"sarfa_groups\",\n    MEMBERS: \"sarfa_members\",\n    PAYMENTS: \"sarfa_payments\",\n    NOTIFICATIONS: \"sarfa_notifications\",\n    CURRENT_USER: \"sarfa_current_user\"\n};\n// دوال مساعدة للتخزين المحلي\nconst storage = {\n    // المجموعات\n    getGroups: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.GROUPS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveGroups: (groups)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups));\n    },\n    addGroup: (group)=>{\n        const newGroup = {\n            ...group,\n            id: generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const groups = storage.getGroups();\n        groups.push(newGroup);\n        storage.saveGroups(groups);\n        return newGroup;\n    },\n    updateGroup: (id, updates)=>{\n        const groups = storage.getGroups();\n        const index = groups.findIndex((g)=>g.id === id);\n        if (index === -1) return null;\n        groups[index] = {\n            ...groups[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        storage.saveGroups(groups);\n        return groups[index];\n    },\n    getGroupById: (id)=>{\n        const groups = storage.getGroups();\n        return groups.find((g)=>g.id === id) || null;\n    },\n    // الأعضاء\n    getMembers: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveMembers: (members)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));\n    },\n    getMembersByGroupId: (groupId)=>{\n        return storage.getMembers().filter((m)=>m.group_id === groupId);\n    },\n    addMember: (member)=>{\n        const newMember = {\n            ...member,\n            id: generateId(),\n            joined_at: new Date().toISOString()\n        };\n        const members = storage.getMembers();\n        members.push(newMember);\n        storage.saveMembers(members);\n        return newMember;\n    },\n    // المدفوعات\n    getPayments: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS);\n        return data ? JSON.parse(data) : [];\n    },\n    savePayments: (payments)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments));\n    },\n    getPaymentsByGroupId: (groupId)=>{\n        return storage.getPayments().filter((p)=>p.group_id === groupId);\n    },\n    // الإشعارات\n    getNotifications: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveNotifications: (notifications)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    },\n    addNotification: (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: generateId(),\n            created_at: new Date().toISOString()\n        };\n        const notifications = storage.getNotifications();\n        notifications.unshift(newNotification) // إضافة في المقدمة\n        ;\n        storage.saveNotifications(notifications);\n        return newNotification;\n    },\n    // المستخدم الحالي\n    getCurrentUser: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n        return data ? JSON.parse(data) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));\n    },\n    // مسح جميع البيانات\n    clearAll: ()=>{\n        if (false) {}\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n    }\n};\n// دالة توليد معرف فريد\nfunction generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n// تهيئة البيانات التجريبية\nconst initializeMockData = ()=>{\n    if (false) {}\n    // التحقق من وجود بيانات مسبقة\n    const existingGroups = storage.getGroups();\n    if (existingGroups.length > 0) return;\n    // إنشاء مستخدم تجريبي\n    const mockUser = {\n        id: \"user_1\",\n        email: \"<EMAIL>\",\n        full_name: \"أحمد محمد\",\n        avatar_url: null\n    };\n    storage.setCurrentUser(mockUser);\n    // إنشاء مجموعات تجريبية\n    const mockGroups = [\n        {\n            id: \"group_1\",\n            name: \"صندوق الأصدقاء\",\n            description: \"صندوق تعاوني للأصدقاء المقربين\",\n            admin_id: \"user_1\",\n            member_count: 10,\n            contribution_amount: 1000,\n            cycle_duration_months: 10,\n            status: \"active\",\n            start_date: \"2024-01-01\",\n            end_date: \"2024-10-31\",\n            draw_completed: true,\n            current_cycle: 2,\n            created_at: \"2023-12-15T00:00:00Z\",\n            updated_at: \"2023-12-15T00:00:00Z\"\n        }\n    ];\n    storage.saveGroups(mockGroups);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/storage.ts\n"));

/***/ })

});