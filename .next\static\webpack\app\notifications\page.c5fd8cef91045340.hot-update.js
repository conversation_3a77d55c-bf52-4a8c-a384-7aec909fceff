"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/notifications/page",{

/***/ "(app-pages-browser)/./src/app/notifications/page.tsx":
/*!****************************************!*\
  !*** ./src/app/notifications/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockNotifications = [\n    {\n        id: \"1\",\n        title: \"مساهمة مستحقة\",\n        message: \"مساهمتك الشهرية لصندوق الأصدقاء مستحقة اليوم\",\n        type: \"payment_due\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-15T10:00:00Z\"\n    },\n    {\n        id: \"2\",\n        title: \"تم استلام مساهمة\",\n        message: \"تم استلام مساهمة محمد علي لصندوق الأصدقاء\",\n        type: \"payment_received\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-14T15:30:00Z\"\n    },\n    {\n        id: \"3\",\n        title: \"موعد الصرف\",\n        message: \"سيتم صرف مبلغ 10,000 ريال لسارة أحمد غداً\",\n        type: \"disbursement\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 10000,\n        read: true,\n        created_at: \"2024-02-13T09:00:00Z\"\n    },\n    {\n        id: \"4\",\n        title: \"عضو جديد\",\n        message: \"انضم عبدالله سالم إلى صندوق العائلة\",\n        type: \"general\",\n        group_name: \"صندوق العائلة\",\n        read: true,\n        created_at: \"2024-02-12T14:20:00Z\"\n    },\n    {\n        id: \"5\",\n        title: \"تم إجراء القرعة\",\n        message: \"تم إجراء قرعة صندوق الأصدقاء وتحديد ترتيب الأعضاء\",\n        type: \"general\",\n        group_name: \"صندوق الأصدقاء\",\n        read: true,\n        created_at: \"2024-01-15T11:00:00Z\"\n    }\n];\nfunction NotificationsPage() {\n    _s();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.getCurrentUser();\n        const allNotifications = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.getNotifications();\n        setUser(currentUser);\n        setNotifications(allNotifications);\n    }, []);\n    const filteredNotifications = notifications.filter((notification)=>filter === \"all\" || notification.type === filter);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const markAsRead = async (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = async ()=>{\n        setLoading(true);\n        try {\n            // TODO: Implement actual API call\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            setNotifications((prev)=>prev.map((notification)=>({\n                        ...notification,\n                        read: true\n                    })));\n        } catch (error) {\n            console.error(\"Error marking all as read:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteNotification = async (notificationId)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== notificationId));\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"payment_due\":\n            case \"payment_received\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 16\n                }, this);\n            case \"disbursement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 16\n                }, this);\n            case \"general\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationColor = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"text-red-600 bg-red-100\";\n            case \"payment_received\":\n                return \"text-green-600 bg-green-100\";\n            case \"disbursement\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"general\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getTypeText = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"مساهمة مستحقة\";\n            case \"payment_received\":\n                return \"مساهمة مستلمة\";\n            case \"disbursement\":\n                return \"صرف\";\n            case \"general\":\n                return \"عام\";\n            default:\n                return type;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                        children: unreadCount > 0 ? \"لديك \".concat(unreadCount, \" إشعار غير مقروء\") : \"جميع الإشعارات مقروءة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onClick: markAllAsRead,\n                                loading: loading,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديد الكل كمقروء\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تصفية الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        {\n                                            key: \"all\",\n                                            label: \"الكل\"\n                                        },\n                                        {\n                                            key: \"payment_due\",\n                                            label: \"مساهمات مستحقة\"\n                                        },\n                                        {\n                                            key: \"payment_received\",\n                                            label: \"مساهمات مستلمة\"\n                                        },\n                                        {\n                                            key: \"disbursement\",\n                                            label: \"صرف\"\n                                        },\n                                        {\n                                            key: \"general\",\n                                            label: \"عام\"\n                                        }\n                                    ].map((filterOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilter(filterOption.key),\n                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(filter === filterOption.key ? \"bg-primary-600 text-white\" : \"bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700\"),\n                                            children: filterOption.label\n                                        }, filterOption.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: filteredNotifications.length > 0 ? filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"transition-all hover:shadow-md \".concat(!notification.read ? \"border-primary-200 bg-primary-50/30 dark:bg-primary-900/10\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4 space-x-reverse flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-full \".concat(getNotificationColor(notification.type)),\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold \".concat(!notification.read ? \"text-gray-900 dark:text-white\" : \"text-gray-700 dark:text-gray-300\"),\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: notification.group_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(notification.created_at)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            notification.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-primary-600\",\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(notification.amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"default\",\n                                                                        children: getTypeText(notification.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse mr-4\",\n                                                children: [\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsRead(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-primary-600 transition-colors\",\n                                                        title: \"تحديد كمقروء\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>deleteNotification(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                        title: \"حذف الإشعار\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: filter === \"all\" ? \"لا توجد إشعارات حالياً\" : 'لا توجد إشعارات من نوع \"'.concat(getTypeText(filter), '\"')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationsPage, \"zrvg/Xa6DE2JWAs7Q4S1dHGKG/U=\");\n_c = NotificationsPage;\nvar _c;\n$RefreshReg$(_c, \"NotificationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/notifications/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeMockData: function() { return /* binding */ initializeMockData; },\n/* harmony export */   storage: function() { return /* binding */ storage; }\n/* harmony export */ });\n// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    GROUPS: \"sarfa_groups\",\n    MEMBERS: \"sarfa_members\",\n    PAYMENTS: \"sarfa_payments\",\n    NOTIFICATIONS: \"sarfa_notifications\",\n    CURRENT_USER: \"sarfa_current_user\"\n};\n// دوال مساعدة للتخزين المحلي\nconst storage = {\n    // المجموعات\n    getGroups: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.GROUPS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveGroups: (groups)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups));\n    },\n    addGroup: (group)=>{\n        const newGroup = {\n            ...group,\n            id: generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const groups = storage.getGroups();\n        groups.push(newGroup);\n        storage.saveGroups(groups);\n        return newGroup;\n    },\n    updateGroup: (id, updates)=>{\n        const groups = storage.getGroups();\n        const index = groups.findIndex((g)=>g.id === id);\n        if (index === -1) return null;\n        groups[index] = {\n            ...groups[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        storage.saveGroups(groups);\n        return groups[index];\n    },\n    getGroupById: (id)=>{\n        const groups = storage.getGroups();\n        return groups.find((g)=>g.id === id) || null;\n    },\n    // الأعضاء\n    getMembers: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveMembers: (members)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));\n    },\n    getMembersByGroupId: (groupId)=>{\n        return storage.getMembers().filter((m)=>m.group_id === groupId);\n    },\n    addMember: (member)=>{\n        const newMember = {\n            ...member,\n            id: generateId(),\n            joined_at: new Date().toISOString()\n        };\n        const members = storage.getMembers();\n        members.push(newMember);\n        storage.saveMembers(members);\n        return newMember;\n    },\n    // المدفوعات\n    getPayments: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS);\n        return data ? JSON.parse(data) : [];\n    },\n    savePayments: (payments)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments));\n    },\n    getPaymentsByGroupId: (groupId)=>{\n        return storage.getPayments().filter((p)=>p.group_id === groupId);\n    },\n    // الإشعارات\n    getNotifications: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveNotifications: (notifications)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    },\n    addNotification: (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: generateId(),\n            created_at: new Date().toISOString()\n        };\n        const notifications = storage.getNotifications();\n        notifications.unshift(newNotification) // إضافة في المقدمة\n        ;\n        storage.saveNotifications(notifications);\n        return newNotification;\n    },\n    // المستخدم الحالي\n    getCurrentUser: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n        return data ? JSON.parse(data) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));\n    },\n    // مسح جميع البيانات\n    clearAll: ()=>{\n        if (false) {}\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n    }\n};\n// دالة توليد معرف فريد\nfunction generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n// تهيئة البيانات التجريبية\nconst initializeMockData = ()=>{\n    if (false) {}\n    // التحقق من وجود بيانات مسبقة\n    const existingGroups = storage.getGroups();\n    if (existingGroups.length > 0) return;\n    // إنشاء مستخدم تجريبي\n    const mockUser = {\n        id: \"user_1\",\n        email: \"<EMAIL>\",\n        full_name: \"أحمد محمد\",\n        avatar_url: null\n    };\n    storage.setCurrentUser(mockUser);\n    // إنشاء مجموعات تجريبية\n    const mockGroups = [\n        {\n            id: \"group_1\",\n            name: \"صندوق الأصدقاء\",\n            description: \"صندوق تعاوني للأصدقاء المقربين\",\n            admin_id: \"user_1\",\n            member_count: 10,\n            contribution_amount: 1000,\n            cycle_duration_months: 10,\n            status: \"active\",\n            start_date: \"2024-01-01\",\n            end_date: \"2024-10-31\",\n            draw_completed: true,\n            current_cycle: 2,\n            created_at: \"2023-12-15T00:00:00Z\",\n            updated_at: \"2023-12-15T00:00:00Z\"\n        }\n    ];\n    storage.saveGroups(mockGroups);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/storage.ts\n"));

/***/ })

});