"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./src/app/groups/[id]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/groups/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,CheckCircle,Clock,DollarSign,Settings,Shuffle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shuffle.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* harmony import */ var _components_lottery_LotteryDrawModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/lottery/LotteryDrawModal */ \"(app-pages-browser)/./src/components/lottery/LotteryDrawModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockGroup = {\n    id: \"1\",\n    name: \"صندوق الأصدقاء\",\n    description: \"صندوق تعاوني للأصدقاء المقربين\",\n    admin_id: \"1\",\n    member_count: 10,\n    contribution_amount: 1000,\n    cycle_duration_months: 10,\n    status: \"active\",\n    start_date: \"2024-01-01\",\n    end_date: \"2024-10-31\",\n    draw_completed: true,\n    current_cycle: 2,\n    created_at: \"2023-12-15\"\n};\nconst mockMembers = [\n    {\n        id: \"1\",\n        user_id: \"1\",\n        full_name: \"أحمد محمد\",\n        position_in_draw: 3,\n        is_admin: true,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        user_id: \"2\",\n        full_name: \"محمد علي\",\n        position_in_draw: 1,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        user_id: \"3\",\n        full_name: \"سارة أحمد\",\n        position_in_draw: 2,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"4\",\n        user_id: \"4\",\n        full_name: \"فاطمة محمد\",\n        position_in_draw: 4,\n        is_admin: false,\n        status: \"active\"\n    },\n    {\n        id: \"5\",\n        user_id: \"5\",\n        full_name: \"عبدالله سالم\",\n        position_in_draw: 5,\n        is_admin: false,\n        status: \"active\"\n    }\n];\nconst mockPayments = [\n    {\n        id: \"1\",\n        member_name: \"أحمد محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-01\"\n    },\n    {\n        id: \"2\",\n        member_name: \"محمد علي\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"paid\",\n        paid_date: \"2024-02-02\"\n    },\n    {\n        id: \"3\",\n        member_name: \"سارة أحمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"pending\",\n        due_date: \"2024-02-15\"\n    },\n    {\n        id: \"4\",\n        member_name: \"فاطمة محمد\",\n        cycle_number: 2,\n        amount: 1000,\n        status: \"overdue\",\n        due_date: \"2024-02-10\"\n    }\n];\nfunction GroupDetailsPage(param) {\n    let { params } = param;\n    _s();\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [payments, setPayments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showLotteryModal, setShowLotteryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAdmin = (group === null || group === void 0 ? void 0 : group.admin_id) === (user === null || user === void 0 ? void 0 : user.id);\n    const totalCollected = payments.filter((p)=>p.status === \"paid\").length * group.contribution_amount;\n    const pendingPayments = payments.filter((p)=>p.status === \"pending\").length;\n    const overduePayments = payments.filter((p)=>p.status === \"overdue\").length;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    user: user\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600 dark:text-gray-400\",\n                                children: \"جاري التحميل...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    if (!group) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    user: user\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"المجموعة غير موجودة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                children: \"لم يتم العثور على المجموعة المطلوبة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                onClick: ()=>router.push(\"/groups\"),\n                                children: \"العودة إلى المجموعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getCurrentUser();\n        const groupData = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getGroupById(params.id);\n        const groupMembers = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembersByGroupId(params.id);\n        const groupPayments = _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getPaymentsByGroupId(params.id);\n        setUser(currentUser);\n        setGroup(groupData);\n        setMembers(groupMembers);\n        setPayments(groupPayments);\n        setLoading(false);\n    }, [\n        params.id\n    ]);\n    const handleDrawLottery = async ()=>{\n        setShowLotteryModal(true);\n    };\n    const handleLotteryComplete = (results)=>{\n        try {\n            // تحديث مواضع الأعضاء\n            const updatedMembers = members.map((member)=>{\n                const result = results.find((r)=>r.member_id === member.id);\n                return result ? {\n                    ...member,\n                    position_in_draw: result.position\n                } : member;\n            });\n            // تحديث المجموعة لتكون القرعة مكتملة\n            const updatedGroup = {\n                ...group,\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            };\n            // حفظ التحديثات\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.saveMembers(_lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.getMembers().map((m)=>updatedMembers.find((um)=>um.id === m.id) || m));\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.updateGroup(group.id, {\n                draw_completed: true,\n                status: \"active\",\n                current_cycle: 1\n            });\n            // تحديث الحالة المحلية\n            setMembers(updatedMembers);\n            setGroup(updatedGroup);\n            // إضافة إشعار\n            _lib_storage__WEBPACK_IMPORTED_MODULE_8__.storage.addNotification({\n                user_id: user.id,\n                group_id: group.id,\n                group_name: group.name,\n                title: \"تم إجراء القرعة\",\n                message: 'تم إجراء قرعة مجموعة \"'.concat(group.name, '\" وتحديد ترتيب الأعضاء'),\n                type: \"general\",\n                read: false\n            });\n            setShowLotteryModal(false);\n        } catch (error) {\n            console.error(\"Error completing lottery:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: group.status === \"active\" ? \"success\" : group.status === \"draft\" ? \"warning\" : \"default\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(group.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إعدادات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-primary-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"الأعضاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: group.member_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"المبلغ المجمع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(totalCollected)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-yellow-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدفوعات معلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: overduePayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 space-x-reverse\",\n                                children: [\n                                    {\n                                        id: \"overview\",\n                                        name: \"نظرة عامة\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                                    },\n                                    {\n                                        id: \"members\",\n                                        name: \"الأعضاء\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                    },\n                                    {\n                                        id: \"payments\",\n                                        name: \"المدفوعات\",\n                                        icon: _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات المجموعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"المساهمة الشهرية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(group.contribution_amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"مدة الدورة:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.cycle_duration_months,\n                                                            \" شهر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ البداية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.start_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"تاريخ النهاية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(group.end_date)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"الدورة الحالية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            group.current_cycle,\n                                                            \" من \",\n                                                            group.cycle_duration_months\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"القرعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"ترتيب الصرف للأعضاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: group.draw_completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: members.sort((a, b)=>(a.position_in_draw || 0) - (b.position_in_draw || 0)).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-50 border border-primary-200\" : \"bg-gray-50 dark:bg-gray-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(member.position_in_draw === group.current_cycle ? \"bg-primary-600 text-white\" : \"bg-gray-200 text-gray-700\"),\n                                                                    children: member.position_in_draw\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-3 font-medium\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"info\",\n                                                                    className: \"mr-2\",\n                                                                    children: \"مسؤول\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        member.position_in_draw < group.current_cycle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, member.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"لم يتم إجراء القرعة بعد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onClick: handleDrawLottery,\n                                                    loading: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_CheckCircle_Clock_DollarSign_Settings_Shuffle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"إجراء القرعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"members\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"أعضاء المجموعة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"قائمة بجميع أعضاء الصندوق التعاوني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 font-medium\",\n                                                                children: member.full_name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                    children: member.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: member.position_in_draw ? \"الترتيب: \".concat(member.position_in_draw) : \"لم يتم تحديد الترتيب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        member.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"info\",\n                                                            children: \"مسؤول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: member.status === \"active\" ? \"success\" : \"default\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(member.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"payments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"مدفوعات الدورة الحالية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: [\n                                            \"حالة المدفوعات للدورة رقم \",\n                                            group.current_cycle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: payments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: payment.member_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: payment.status === \"paid\" && payment.paid_date ? \"تم الدفع في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.paid_date)) : payment.status === \"pending\" ? \"مستحق في \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date)) : \"متأخر منذ \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(payment.due_date))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(payment.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: payment.status === \"paid\" ? \"success\" : payment.status === \"pending\" ? \"warning\" : \"danger\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusText)(payment.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isAdmin && payment.status !== \"paid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            children: \"تأكيد الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, payment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            showLotteryModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lottery_LotteryDrawModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showLotteryModal,\n                onClose: ()=>setShowLotteryModal(false),\n                members: members,\n                onLotteryComplete: handleLotteryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\[id]\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetailsPage, \"1HTPcEVpDd+PhyViXfW7dgFeX5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GroupDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/[id]/page.tsx\n"));

/***/ })

});