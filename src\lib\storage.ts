// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)

export interface Group {
  id: string
  name: string
  description: string | null
  admin_id: string
  member_count: number
  contribution_amount: number
  cycle_duration_months: number
  status: 'draft' | 'active' | 'completed' | 'cancelled'
  start_date: string | null
  end_date: string | null
  draw_completed: boolean
  current_cycle: number
  created_at: string
  updated_at: string
}

export interface Member {
  id: string
  group_id: string
  user_id: string
  full_name: string
  position_in_draw: number | null
  is_admin: boolean
  joined_at: string
  status: 'active' | 'inactive' | 'removed'
}

export interface Payment {
  id: string
  group_id: string
  member_id: string
  member_name: string
  cycle_number: number
  amount: number
  due_date: string
  paid_date: string | null
  status: 'pending' | 'paid' | 'overdue'
  payment_method: string | null
  notes: string | null
  created_at: string
  updated_at: string
}

export interface Notification {
  id: string
  user_id: string
  group_id: string | null
  group_name?: string
  title: string
  message: string
  type: 'payment_due' | 'payment_received' | 'disbursement' | 'general'
  amount?: number
  read: boolean
  created_at: string
}

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  GROUPS: 'sarfa_groups',
  MEMBERS: 'sarfa_members',
  PAYMENTS: 'sarfa_payments',
  NOTIFICATIONS: 'sarfa_notifications',
  CURRENT_USER: 'sarfa_current_user'
}

// دوال مساعدة للتخزين المحلي
export const storage = {
  // المجموعات
  getGroups: (): Group[] => {
    if (typeof window === 'undefined') return []
    const data = localStorage.getItem(STORAGE_KEYS.GROUPS)
    return data ? JSON.parse(data) : []
  },

  saveGroups: (groups: Group[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups))
  },

  addGroup: (group: Omit<Group, 'id' | 'created_at' | 'updated_at'>): Group => {
    const newGroup: Group = {
      ...group,
      id: generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    const groups = storage.getGroups()
    groups.push(newGroup)
    storage.saveGroups(groups)
    
    return newGroup
  },

  updateGroup: (id: string, updates: Partial<Group>): Group | null => {
    const groups = storage.getGroups()
    const index = groups.findIndex(g => g.id === id)
    
    if (index === -1) return null
    
    groups[index] = {
      ...groups[index],
      ...updates,
      updated_at: new Date().toISOString()
    }
    
    storage.saveGroups(groups)
    return groups[index]
  },

  getGroupById: (id: string): Group | null => {
    const groups = storage.getGroups()
    return groups.find(g => g.id === id) || null
  },

  // الأعضاء
  getMembers: (): Member[] => {
    if (typeof window === 'undefined') return []
    const data = localStorage.getItem(STORAGE_KEYS.MEMBERS)
    return data ? JSON.parse(data) : []
  },

  saveMembers: (members: Member[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members))
  },

  getMembersByGroupId: (groupId: string): Member[] => {
    return storage.getMembers().filter(m => m.group_id === groupId)
  },

  addMember: (member: Omit<Member, 'id' | 'joined_at'>): Member => {
    const newMember: Member = {
      ...member,
      id: generateId(),
      joined_at: new Date().toISOString()
    }
    
    const members = storage.getMembers()
    members.push(newMember)
    storage.saveMembers(members)
    
    return newMember
  },

  // المدفوعات
  getPayments: (): Payment[] => {
    if (typeof window === 'undefined') return []
    const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS)
    return data ? JSON.parse(data) : []
  },

  savePayments: (payments: Payment[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments))
  },

  getPaymentsByGroupId: (groupId: string): Payment[] => {
    return storage.getPayments().filter(p => p.group_id === groupId)
  },

  // الإشعارات
  getNotifications: (): Notification[] => {
    if (typeof window === 'undefined') return []
    const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS)
    return data ? JSON.parse(data) : []
  },

  saveNotifications: (notifications: Notification[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))
  },

  addNotification: (notification: Omit<Notification, 'id' | 'created_at'>): Notification => {
    const newNotification: Notification = {
      ...notification,
      id: generateId(),
      created_at: new Date().toISOString()
    }
    
    const notifications = storage.getNotifications()
    notifications.unshift(newNotification) // إضافة في المقدمة
    storage.saveNotifications(notifications)
    
    return newNotification
  },

  // المستخدم الحالي
  getCurrentUser: () => {
    if (typeof window === 'undefined') return null
    const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER)
    return data ? JSON.parse(data) : null
  },

  setCurrentUser: (user: any) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user))
  },

  // مسح جميع البيانات
  clearAll: () => {
    if (typeof window === 'undefined') return
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })
  }
}

// دالة توليد معرف فريد
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// تهيئة البيانات التجريبية
export const initializeMockData = () => {
  if (typeof window === 'undefined') return
  
  // التحقق من وجود بيانات مسبقة
  const existingGroups = storage.getGroups()
  if (existingGroups.length > 0) return
  
  // إنشاء مستخدم تجريبي
  const mockUser = {
    id: 'user_1',
    email: '<EMAIL>',
    full_name: 'أحمد محمد',
    avatar_url: null
  }
  storage.setCurrentUser(mockUser)
  
  // إنشاء مجموعات تجريبية
  const mockGroups: Group[] = [
    {
      id: 'group_1',
      name: 'صندوق الأصدقاء',
      description: 'صندوق تعاوني للأصدقاء المقربين',
      admin_id: 'user_1',
      member_count: 10,
      contribution_amount: 500,
      cycle_duration_months: 10,
      status: 'active',
      start_date: '2024-01-01',
      end_date: '2024-10-31',
      draw_completed: true,
      current_cycle: 2,
      created_at: '2023-12-15T00:00:00Z',
      updated_at: '2023-12-15T00:00:00Z'
    }
  ]
  
  storage.saveGroups(mockGroups)
}
