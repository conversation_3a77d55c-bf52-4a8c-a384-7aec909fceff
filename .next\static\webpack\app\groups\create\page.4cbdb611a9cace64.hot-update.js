"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/create/page",{

/***/ "(app-pages-browser)/./src/app/groups/create/page.tsx":
/*!****************************************!*\
  !*** ./src/app/groups/create/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateGroupPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock user data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nfunction CreateGroupPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        memberCount: \"\",\n        contributionAmount: \"\",\n        cycleDurationMonths: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"اسم المجموعة مطلوب\";\n        }\n        if (!formData.memberCount) {\n            newErrors.memberCount = \"عدد الأعضاء مطلوب\";\n        } else if (parseInt(formData.memberCount) < 2) {\n            newErrors.memberCount = \"يجب أن يكون عدد الأعضاء 2 على الأقل\";\n        } else if (parseInt(formData.memberCount) > 50) {\n            newErrors.memberCount = \"عدد الأعضاء لا يمكن أن يزيد عن 50\";\n        }\n        if (!formData.contributionAmount) {\n            newErrors.contributionAmount = \"مبلغ المساهمة مطلوب\";\n        } else if (parseFloat(formData.contributionAmount) <= 0) {\n            newErrors.contributionAmount = \"مبلغ المساهمة يجب أن يكون أكبر من صفر\";\n        }\n        if (!formData.cycleDurationMonths) {\n            newErrors.cycleDurationMonths = \"مدة الدورة مطلوبة\";\n        } else if (parseInt(formData.cycleDurationMonths) < 1) {\n            newErrors.cycleDurationMonths = \"مدة الدورة يجب أن تكون شهر واحد على الأقل\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        try {\n            // الحصول على المستخدم الحالي\n            const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.getCurrentUser();\n            if (!currentUser) {\n                setErrors({\n                    general: \"يجب تسجيل الدخول أولاً\"\n                });\n                setLoading(false);\n                return;\n            }\n            // إنشاء المجموعة الجديدة\n            const newGroup = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.addGroup({\n                name: formData.name,\n                description: formData.description || null,\n                admin_id: currentUser.id,\n                member_count: parseInt(formData.memberCount),\n                contribution_amount: parseFloat(formData.contributionAmount),\n                cycle_duration_months: parseInt(formData.cycleDurationMonths),\n                status: \"draft\",\n                start_date: null,\n                end_date: null,\n                draw_completed: false,\n                current_cycle: 0\n            });\n            // إضافة المستخدم الحالي كعضو ومسؤول\n            _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.addMember({\n                group_id: newGroup.id,\n                user_id: currentUser.id,\n                full_name: currentUser.full_name,\n                position_in_draw: null,\n                is_admin: true,\n                status: \"active\"\n            });\n            // إضافة إشعار\n            _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.addNotification({\n                user_id: currentUser.id,\n                group_id: newGroup.id,\n                group_name: newGroup.name,\n                title: \"تم إنشاء مجموعة جديدة\",\n                message: 'تم إنشاء مجموعة \"'.concat(newGroup.name, '\" بنجاح'),\n                type: \"general\",\n                read: false\n            });\n            // محاكاة تأخير API\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // التوجه إلى صفحة تفاصيل المجموعة\n            router.push(\"/groups/\".concat(newGroup.id));\n        } catch (error) {\n            console.error(\"Group creation error:\", error);\n            setErrors({\n                general: \"حدث خطأ أثناء إنشاء المجموعة. يرجى المحاولة مرة أخرى.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const totalAmount = formData.memberCount && formData.contributionAmount ? parseInt(formData.memberCount) * parseFloat(formData.contributionAmount) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"ghost\",\n                                onClick: ()=>router.back(),\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"العودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: \"إنشاء مجموعة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                children: \"أنشئ صندوق تعاوني جديد وادع الأعضاء للانضمام\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                    children: \"تفاصيل المجموعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                    children: \"أدخل المعلومات الأساسية للصندوق التعاوني\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        label: \"اسم المجموعة\",\n                                                        placeholder: \"مثال: صندوق الأصدقاء\",\n                                                        value: formData.name,\n                                                        onChange: handleInputChange,\n                                                        error: errors.name,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                children: \"الوصف (اختياري)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"description\",\n                                                                placeholder: \"وصف مختصر عن المجموعة وهدفها\",\n                                                                value: formData.description,\n                                                                onChange: handleInputChange,\n                                                                rows: 3,\n                                                                className: \"block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        type: \"number\",\n                                                        name: \"memberCount\",\n                                                        label: \"عدد الأعضاء\",\n                                                        placeholder: \"10\",\n                                                        value: formData.memberCount,\n                                                        onChange: handleInputChange,\n                                                        error: errors.memberCount,\n                                                        min: \"2\",\n                                                        max: \"50\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        type: \"number\",\n                                                        name: \"contributionAmount\",\n                                                        label: \"مبلغ المساهمة الشهرية (ريال)\",\n                                                        placeholder: \"1000\",\n                                                        value: formData.contributionAmount,\n                                                        onChange: handleInputChange,\n                                                        error: errors.contributionAmount,\n                                                        min: \"1\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        type: \"number\",\n                                                        name: \"cycleDurationMonths\",\n                                                        label: \"مدة الدورة (بالأشهر)\",\n                                                        placeholder: \"12\",\n                                                        value: formData.cycleDurationMonths,\n                                                        onChange: handleInputChange,\n                                                        error: errors.cycleDurationMonths,\n                                                        min: \"1\",\n                                                        helperText: \"عادة ما تكون مساوية لعدد الأعضاء\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm\",\n                                                        children: errors.general\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-4 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.back(),\n                                                                children: \"إلغاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                type: \"submit\",\n                                                                loading: loading,\n                                                                children: \"إنشاء المجموعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"ملخص المجموعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                            children: \"عدد الأعضاء:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                formData.memberCount || \"0\",\n                                                                \" عضو\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                            children: \"المساهمة الشهرية:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: formData.contributionAmount ? \"\".concat(formData.contributionAmount, \" ريال\") : \"0 ريال\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                            children: \"مدة الدورة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                formData.cycleDurationMonths || \"0\",\n                                                                \" شهر\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-gray-200 dark:border-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-lg font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"إجمالي المبلغ الشهري:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-primary-600\",\n                                                            children: [\n                                                                totalAmount.toLocaleString(),\n                                                                \" ريال\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary-50 dark:bg-primary-900/20 p-4 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-700 dark:text-primary-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ملاحظة:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" سيتم توزيع المبلغ الإجمالي على الأعضاء حسب ترتيب القرعة كل شهر.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\groups\\\\create\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateGroupPage, \"tlSEQbiC40BcueOLBCEMauKQRpc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CreateGroupPage;\nvar _c;\n$RefreshReg$(_c, \"CreateGroupPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/groups/create/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeMockData: function() { return /* binding */ initializeMockData; },\n/* harmony export */   storage: function() { return /* binding */ storage; }\n/* harmony export */ });\n// نظام تخزين محلي للبيانات (مؤقت حتى يتم ربط Supabase)\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    GROUPS: \"sarfa_groups\",\n    MEMBERS: \"sarfa_members\",\n    PAYMENTS: \"sarfa_payments\",\n    NOTIFICATIONS: \"sarfa_notifications\",\n    CURRENT_USER: \"sarfa_current_user\"\n};\n// دوال مساعدة للتخزين المحلي\nconst storage = {\n    // المجموعات\n    getGroups: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.GROUPS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveGroups: (groups)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groups));\n    },\n    addGroup: (group)=>{\n        const newGroup = {\n            ...group,\n            id: generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const groups = storage.getGroups();\n        groups.push(newGroup);\n        storage.saveGroups(groups);\n        return newGroup;\n    },\n    updateGroup: (id, updates)=>{\n        const groups = storage.getGroups();\n        const index = groups.findIndex((g)=>g.id === id);\n        if (index === -1) return null;\n        groups[index] = {\n            ...groups[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        storage.saveGroups(groups);\n        return groups[index];\n    },\n    getGroupById: (id)=>{\n        const groups = storage.getGroups();\n        return groups.find((g)=>g.id === id) || null;\n    },\n    // الأعضاء\n    getMembers: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveMembers: (members)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));\n    },\n    getMembersByGroupId: (groupId)=>{\n        return storage.getMembers().filter((m)=>m.group_id === groupId);\n    },\n    addMember: (member)=>{\n        const newMember = {\n            ...member,\n            id: generateId(),\n            joined_at: new Date().toISOString()\n        };\n        const members = storage.getMembers();\n        members.push(newMember);\n        storage.saveMembers(members);\n        return newMember;\n    },\n    // المدفوعات\n    getPayments: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.PAYMENTS);\n        return data ? JSON.parse(data) : [];\n    },\n    savePayments: (payments)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.PAYMENTS, JSON.stringify(payments));\n    },\n    getPaymentsByGroupId: (groupId)=>{\n        return storage.getPayments().filter((p)=>p.group_id === groupId);\n    },\n    // الإشعارات\n    getNotifications: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n        return data ? JSON.parse(data) : [];\n    },\n    saveNotifications: (notifications)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    },\n    addNotification: (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: generateId(),\n            created_at: new Date().toISOString()\n        };\n        const notifications = storage.getNotifications();\n        notifications.unshift(newNotification) // إضافة في المقدمة\n        ;\n        storage.saveNotifications(notifications);\n        return newNotification;\n    },\n    // المستخدم الحالي\n    getCurrentUser: ()=>{\n        if (false) {}\n        const data = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n        return data ? JSON.parse(data) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));\n    },\n    // مسح جميع البيانات\n    clearAll: ()=>{\n        if (false) {}\n        Object.values(STORAGE_KEYS).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n    }\n};\n// دالة توليد معرف فريد\nfunction generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n// تهيئة البيانات التجريبية\nconst initializeMockData = ()=>{\n    if (false) {}\n    // التحقق من وجود بيانات مسبقة\n    const existingGroups = storage.getGroups();\n    if (existingGroups.length > 0) return;\n    // إنشاء مستخدم تجريبي\n    const mockUser = {\n        id: \"user_1\",\n        email: \"<EMAIL>\",\n        full_name: \"أحمد محمد\",\n        avatar_url: null\n    };\n    storage.setCurrentUser(mockUser);\n    // إنشاء مجموعات تجريبية\n    const mockGroups = [\n        {\n            id: \"group_1\",\n            name: \"صندوق الأصدقاء\",\n            description: \"صندوق تعاوني للأصدقاء المقربين\",\n            admin_id: \"user_1\",\n            member_count: 10,\n            contribution_amount: 1000,\n            cycle_duration_months: 10,\n            status: \"active\",\n            start_date: \"2024-01-01\",\n            end_date: \"2024-10-31\",\n            draw_completed: true,\n            current_cycle: 2,\n            created_at: \"2023-12-15T00:00:00Z\",\n            updated_at: \"2023-12-15T00:00:00Z\"\n        }\n    ];\n    storage.saveGroups(mockGroups);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/storage.ts\n"));

/***/ })

});