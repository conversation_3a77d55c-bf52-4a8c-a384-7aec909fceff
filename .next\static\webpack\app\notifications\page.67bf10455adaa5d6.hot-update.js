"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/notifications/page",{

/***/ "(app-pages-browser)/./src/app/notifications/page.tsx":
/*!****************************************!*\
  !*** ./src/app/notifications/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,CheckCircle,DollarSign,Filter,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(app-pages-browser)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockUser = {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    full_name: \"أحمد محمد\",\n    avatar_url: null\n};\nconst mockNotifications = [\n    {\n        id: \"1\",\n        title: \"مساهمة مستحقة\",\n        message: \"مساهمتك الشهرية لصندوق الأصدقاء مستحقة اليوم\",\n        type: \"payment_due\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-15T10:00:00Z\"\n    },\n    {\n        id: \"2\",\n        title: \"تم استلام مساهمة\",\n        message: \"تم استلام مساهمة محمد علي لصندوق الأصدقاء\",\n        type: \"payment_received\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 1000,\n        read: false,\n        created_at: \"2024-02-14T15:30:00Z\"\n    },\n    {\n        id: \"3\",\n        title: \"موعد الصرف\",\n        message: \"سيتم صرف مبلغ 10,000 ريال لسارة أحمد غداً\",\n        type: \"disbursement\",\n        group_name: \"صندوق الأصدقاء\",\n        amount: 10000,\n        read: true,\n        created_at: \"2024-02-13T09:00:00Z\"\n    },\n    {\n        id: \"4\",\n        title: \"عضو جديد\",\n        message: \"انضم عبدالله سالم إلى صندوق العائلة\",\n        type: \"general\",\n        group_name: \"صندوق العائلة\",\n        read: true,\n        created_at: \"2024-02-12T14:20:00Z\"\n    },\n    {\n        id: \"5\",\n        title: \"تم إجراء القرعة\",\n        message: \"تم إجراء قرعة صندوق الأصدقاء وتحديد ترتيب الأعضاء\",\n        type: \"general\",\n        group_name: \"صندوق الأصدقاء\",\n        read: true,\n        created_at: \"2024-01-15T11:00:00Z\"\n    }\n];\nfunction NotificationsPage() {\n    _s();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل البيانات من التخزين المحلي\n        const currentUser = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.getCurrentUser();\n        const allNotifications = _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.getNotifications();\n        setUser(currentUser);\n        setNotifications(allNotifications);\n    }, []);\n    const filteredNotifications = notifications.filter((notification)=>filter === \"all\" || notification.type === filter);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const markAsRead = async (notificationId)=>{\n        const updatedNotifications = notifications.map((notification)=>notification.id === notificationId ? {\n                ...notification,\n                read: true\n            } : notification);\n        setNotifications(updatedNotifications);\n        _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.saveNotifications(updatedNotifications);\n    };\n    const markAllAsRead = async ()=>{\n        setLoading(true);\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            const updatedNotifications = notifications.map((notification)=>({\n                    ...notification,\n                    read: true\n                }));\n            setNotifications(updatedNotifications);\n            _lib_storage__WEBPACK_IMPORTED_MODULE_7__.storage.saveNotifications(updatedNotifications);\n        } catch (error) {\n            console.error(\"Error marking all as read:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteNotification = async (notificationId)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== notificationId));\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"payment_due\":\n            case \"payment_received\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 16\n                }, this);\n            case \"disbursement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case \"general\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationColor = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"text-red-600 bg-red-100\";\n            case \"payment_received\":\n                return \"text-green-600 bg-green-100\";\n            case \"disbursement\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"general\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getTypeText = (type)=>{\n        switch(type){\n            case \"payment_due\":\n                return \"مساهمة مستحقة\";\n            case \"payment_received\":\n                return \"مساهمة مستلمة\";\n            case \"disbursement\":\n                return \"صرف\";\n            case \"general\":\n                return \"عام\";\n            default:\n                return type;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: mockUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mt-2\",\n                                        children: unreadCount > 0 ? \"لديك \".concat(unreadCount, \" إشعار غير مقروء\") : \"جميع الإشعارات مقروءة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onClick: markAllAsRead,\n                                loading: loading,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديد الكل كمقروء\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تصفية الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        {\n                                            key: \"all\",\n                                            label: \"الكل\"\n                                        },\n                                        {\n                                            key: \"payment_due\",\n                                            label: \"مساهمات مستحقة\"\n                                        },\n                                        {\n                                            key: \"payment_received\",\n                                            label: \"مساهمات مستلمة\"\n                                        },\n                                        {\n                                            key: \"disbursement\",\n                                            label: \"صرف\"\n                                        },\n                                        {\n                                            key: \"general\",\n                                            label: \"عام\"\n                                        }\n                                    ].map((filterOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilter(filterOption.key),\n                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(filter === filterOption.key ? \"bg-primary-600 text-white\" : \"bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700\"),\n                                            children: filterOption.label\n                                        }, filterOption.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: filteredNotifications.length > 0 ? filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"transition-all hover:shadow-md \".concat(!notification.read ? \"border-primary-200 bg-primary-50/30 dark:bg-primary-900/10\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4 space-x-reverse flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-full \".concat(getNotificationColor(notification.type)),\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold \".concat(!notification.read ? \"text-gray-900 dark:text-white\" : \"text-gray-700 dark:text-gray-300\"),\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: notification.group_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(notification.created_at)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            notification.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-primary-600\",\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(notification.amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                                lineNumber: 269,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"default\",\n                                                                        children: getTypeText(notification.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse mr-4\",\n                                                children: [\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsRead(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-primary-600 transition-colors\",\n                                                        title: \"تحديد كمقروء\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>deleteNotification(notification.id),\n                                                        className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                        title: \"حذف الإشعار\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_CheckCircle_DollarSign_Filter_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: filter === \"all\" ? \"لا توجد إشعارات حالياً\" : 'لا توجد إشعارات من نوع \"'.concat(getTypeText(filter), '\"')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\صرفة صندوق\\\\src\\\\app\\\\notifications\\\\page.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationsPage, \"zrvg/Xa6DE2JWAs7Q4S1dHGKG/U=\");\n_c = NotificationsPage;\nvar _c;\n$RefreshReg$(_c, \"NotificationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/notifications/page.tsx\n"));

/***/ })

});