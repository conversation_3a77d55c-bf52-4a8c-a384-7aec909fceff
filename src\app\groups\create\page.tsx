'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Users, DollarSign, Calendar, Info } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'

// Mock user data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'أحمد محمد',
  avatar_url: null
}

export default function CreateGroupPage() {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    memberCount: '',
    contributionAmount: '',
    cycleDurationMonths: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المجموعة مطلوب'
    }

    if (!formData.memberCount) {
      newErrors.memberCount = 'عدد الأعضاء مطلوب'
    } else if (parseInt(formData.memberCount) < 2) {
      newErrors.memberCount = 'يجب أن يكون عدد الأعضاء 2 على الأقل'
    } else if (parseInt(formData.memberCount) > 50) {
      newErrors.memberCount = 'عدد الأعضاء لا يمكن أن يزيد عن 50'
    }

    if (!formData.contributionAmount) {
      newErrors.contributionAmount = 'مبلغ المساهمة مطلوب'
    } else if (parseFloat(formData.contributionAmount) <= 0) {
      newErrors.contributionAmount = 'مبلغ المساهمة يجب أن يكون أكبر من صفر'
    }

    if (!formData.cycleDurationMonths) {
      newErrors.cycleDurationMonths = 'مدة الدورة مطلوبة'
    } else if (parseInt(formData.cycleDurationMonths) < 1) {
      newErrors.cycleDurationMonths = 'مدة الدورة يجب أن تكون شهر واحد على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    
    try {
      // TODO: Implement actual group creation with Supabase
      console.log('Creating group:', formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect to group details on success
      router.push('/groups/1') // Replace with actual group ID
    } catch (error) {
      console.error('Group creation error:', error)
      setErrors({ general: 'حدث خطأ أثناء إنشاء المجموعة. يرجى المحاولة مرة أخرى.' })
    } finally {
      setLoading(false)
    }
  }

  const totalAmount = formData.memberCount && formData.contributionAmount 
    ? parseInt(formData.memberCount) * parseFloat(formData.contributionAmount)
    : 0

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar user={mockUser} />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 ml-2" />
            العودة
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            إنشاء مجموعة جديدة
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            أنشئ صندوق تعاوني جديد وادع الأعضاء للانضمام
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل المجموعة</CardTitle>
                <CardDescription>
                  أدخل المعلومات الأساسية للصندوق التعاوني
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Group Name */}
                  <Input
                    type="text"
                    name="name"
                    label="اسم المجموعة"
                    placeholder="مثال: صندوق الأصدقاء"
                    value={formData.name}
                    onChange={handleInputChange}
                    error={errors.name}
                    required
                  />

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      الوصف (اختياري)
                    </label>
                    <textarea
                      name="description"
                      placeholder="وصف مختصر عن المجموعة وهدفها"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-500"
                    />
                  </div>

                  {/* Member Count */}
                  <Input
                    type="number"
                    name="memberCount"
                    label="عدد الأعضاء"
                    placeholder="10"
                    value={formData.memberCount}
                    onChange={handleInputChange}
                    error={errors.memberCount}
                    min="2"
                    max="50"
                    required
                  />

                  {/* Contribution Amount */}
                  <Input
                    type="number"
                    name="contributionAmount"
                    label="مبلغ المساهمة الشهرية (ريال)"
                    placeholder="1000"
                    value={formData.contributionAmount}
                    onChange={handleInputChange}
                    error={errors.contributionAmount}
                    min="1"
                    step="0.01"
                    required
                  />

                  {/* Cycle Duration */}
                  <Input
                    type="number"
                    name="cycleDurationMonths"
                    label="مدة الدورة (بالأشهر)"
                    placeholder="12"
                    value={formData.cycleDurationMonths}
                    onChange={handleInputChange}
                    error={errors.cycleDurationMonths}
                    min="1"
                    helperText="عادة ما تكون مساوية لعدد الأعضاء"
                    required
                  />

                  {/* General Error */}
                  {errors.general && (
                    <div className="text-red-600 text-sm">{errors.general}</div>
                  )}

                  {/* Submit Button */}
                  <div className="flex justify-end space-x-4 space-x-reverse">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                    >
                      إلغاء
                    </Button>
                    <Button
                      type="submit"
                      loading={loading}
                    >
                      إنشاء المجموعة
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Summary */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Info className="w-5 h-5 ml-2" />
                  ملخص المجموعة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">عدد الأعضاء:</span>
                  <span className="font-medium">
                    {formData.memberCount || '0'} عضو
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">المساهمة الشهرية:</span>
                  <span className="font-medium">
                    {formData.contributionAmount ? `${formData.contributionAmount} ريال` : '0 ريال'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">مدة الدورة:</span>
                  <span className="font-medium">
                    {formData.cycleDurationMonths || '0'} شهر
                  </span>
                </div>
                
                <hr className="border-gray-200 dark:border-gray-700" />
                
                <div className="flex items-center justify-between text-lg font-semibold">
                  <span>إجمالي المبلغ الشهري:</span>
                  <span className="text-primary-600">
                    {totalAmount.toLocaleString()} ريال
                  </span>
                </div>
                
                <div className="bg-primary-50 dark:bg-primary-900/20 p-4 rounded-lg">
                  <p className="text-sm text-primary-700 dark:text-primary-300">
                    <strong>ملاحظة:</strong> سيتم توزيع المبلغ الإجمالي على الأعضاء حسب ترتيب القرعة كل شهر.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
