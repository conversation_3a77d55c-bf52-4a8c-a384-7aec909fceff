import Link from 'next/link'
import { Users, DollarSign, Calendar, Shield } from 'lucide-react'
import Navbar from '@/components/layout/Navbar'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800">
      <Navbar />
      <main className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-primary-600 mb-4">صرفة</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            تطبيق إدارة الصناديق التعاونية الدورية - نظم مساهماتك المالية بسهولة وشفافية
          </p>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center">
            <Users className="w-12 h-12 text-primary-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">إدارة الأعضاء</h3>
            <p className="text-gray-600 dark:text-gray-300">
              إضافة وإدارة أعضاء المجموعة بسهولة
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center">
            <DollarSign className="w-12 h-12 text-primary-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">تتبع المساهمات</h3>
            <p className="text-gray-600 dark:text-gray-300">
              متابعة المدفوعات والمبالغ المستحقة
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center">
            <Calendar className="w-12 h-12 text-primary-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">جدولة القرعة</h3>
            <p className="text-gray-600 dark:text-gray-300">
              قرعة عادلة لتحديد ترتيب الصرف
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg text-center">
            <Shield className="w-12 h-12 text-primary-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">الأمان والشفافية</h3>
            <p className="text-gray-600 dark:text-gray-300">
              نظام آمن وشفاف لجميع المعاملات
            </p>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="text-center space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <Link
            href="/auth/login"
            className="inline-block bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            تسجيل الدخول
          </Link>
          <Link
            href="/auth/register"
            className="inline-block bg-white hover:bg-gray-50 text-primary-600 font-semibold py-3 px-8 rounded-lg border-2 border-primary-600 transition-colors duration-200"
          >
            إنشاء حساب جديد
          </Link>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 text-gray-500 dark:text-gray-400">
          <p>© 2024 صرفة - جميع الحقوق محفوظة</p>
        </div>
      </main>
    </div>
  )
}
